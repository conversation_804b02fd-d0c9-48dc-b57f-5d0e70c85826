2025-05-27T13:35:06.029+02:00  INFO 19520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 19520 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-27T13:35:06.036+02:00  INFO 19520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-27T13:35:06.076+02:00  INFO 19520 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-05-27T13:35:06.076+02:00  INFO 19520 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-05-27T13:35:06.795+02:00  INFO 19520 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-27T13:35:06.878+02:00  INFO 19520 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 71 ms. Found 10 JPA repository interfaces.
2025-05-27T13:35:07.438+02:00  INFO 19520 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-27T13:35:07.450+02:00  INFO 19520 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-27T13:35:07.450+02:00  INFO 19520 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-27T13:35:07.490+02:00  INFO 19520 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-27T13:35:07.490+02:00  INFO 19520 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1414 ms
2025-05-27T13:35:07.667+02:00  INFO 19520 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-27T13:35:07.729+02:00  INFO 19520 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.15.Final
2025-05-27T13:35:07.760+02:00  INFO 19520 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-27T13:35:08.043+02:00  INFO 19520 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-27T13:35:08.073+02:00  INFO 19520 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-05-27T13:35:08.969+02:00  INFO 19520 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@27bc26d0
2025-05-27T13:35:08.970+02:00  INFO 19520 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-05-27T13:35:09.261+02:00  INFO 19520 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-27T13:35:10.183+02:00  INFO 19520 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-27T13:35:12.439+02:00  INFO 19520 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T13:35:12.481+02:00  WARN 19520 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-27T13:35:12.702+02:00  INFO 19520 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-05-27T13:35:14.346+02:00  WARN 19520 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-27T13:35:14.378+02:00  INFO 19520 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-27T13:35:14.587+02:00  INFO 19520 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-27T13:35:15.036+02:00  INFO 19520 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-27T13:35:15.070+02:00  INFO 19520 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-27T13:35:15.076+02:00  INFO 19520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 9.464 seconds (process running for 10.453)
2025-05-27T13:39:34.805+02:00  INFO 19520 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-27T13:39:34.805+02:00  INFO 19520 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-27T13:39:34.806+02:00  INFO 19520 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-05-27T13:39:41.685+02:00  WARN 19520 --- [http-nio-8080-exec-7] c.h.evmodbus.services.ReportingService   : No serial ports found
2025-05-27T13:39:41.690+02:00  WARN 19520 --- [http-nio-8080-exec-7] c.h.evmodbus.services.ReportingService   : No serial ports found
2025-05-27T13:39:48.587+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T13:39:48.587644700
2025-05-27T13:39:48.661+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T13:39:48.587644700 using time unit HOUR
2025-05-27T13:39:48.661+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T14:39:48.587644700
2025-05-27T13:39:48.719+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Found 15 readings in extended date range
2025-05-27T13:39:48.719+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Generated 14 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00]
2025-05-27T13:39:48.719+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 14 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00]
2025-05-27T13:39:48.719+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T13:00:00.246831 = 1546.947
2025-05-27T13:39:48.719+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0}
2025-05-27T13:39:48.719+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T13:39:48.719157200
2025-05-27T13:39:48.798+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T13:39:48.719157200 using time unit DAY
2025-05-27T13:39:48.798+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T13:39:48.861+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Found 662 readings in extended date range
2025-05-27T13:39:48.861+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T13:39:48.861+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T13:39:48.861+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T13:00:00.246831 = 1546.947
2025-05-27T13:39:48.861+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T13:39:48.877+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T13:39:48.957+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T13:39:48.957+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T13:39:49.036+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Found 4228 readings in extended date range
2025-05-27T13:39:49.036+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T13:39:49.036+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T13:39:49.036+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T13:00:00.246831 = 1546.947
2025-05-27T13:39:49.036+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T13:39:49.036+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T13:39:49.036+02:00  INFO 19520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T13:39:56.039+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999
2025-05-27T13:39:56.103+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999 using time unit HOUR
2025-05-27T13:39:56.103+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-25T23:00 to 2025-05-27T00:59:59.999999999
2025-05-27T13:39:56.168+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Found 26 readings in extended date range
2025-05-27T13:39:56.168+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Generated 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T13:39:56.168+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T13:39:56.168+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-25T23:00:00.203725 = 1536.977, Last reading: 2025-05-27T00:00:00.200409 = 1546.675
2025-05-27T13:39:56.168+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.0, 01:00 - 02:00=0.0, 02:00 - 03:00=0.0, 03:00 - 04:00=0.0, 04:00 - 05:00=0.0, 05:00 - 06:00=0.0, 06:00 - 07:00=0.0, 07:00 - 08:00=0.0, 08:00 - 09:00=0.0, 09:00 - 10:00=0.0, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0, 15:00 - 16:00=0.0, 16:00 - 17:00=0.0, 17:00 - 18:00=0.0, 18:00 - 19:00=0.0, 19:00 - 20:00=0.3869629, 20:00 - 21:00=2.3790283, 21:00 - 22:00=2.3900146, 22:00 - 23:00=2.369995, 23:00 - 00:00=2.171997}
2025-05-27T13:39:56.168+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T13:39:56.168250800
2025-05-27T13:39:56.232+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T13:39:56.168250800 using time unit DAY
2025-05-27T13:39:56.232+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T13:39:56.306+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Found 662 readings in extended date range
2025-05-27T13:39:56.306+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T13:39:56.306+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T13:39:56.306+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T13:00:00.246831 = 1546.947
2025-05-27T13:39:56.306+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T13:39:56.306+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T13:39:56.370+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T13:39:56.386+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T13:39:56.451+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Found 4228 readings in extended date range
2025-05-27T13:39:56.451+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T13:39:56.451+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T13:39:56.451+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T13:00:00.246831 = 1546.947
2025-05-27T13:39:56.451+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T13:39:56.451+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T13:39:56.451+02:00  INFO 19520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T13:40:03.862+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-25T00:00 to 2025-05-25T23:59:59.999999999
2025-05-27T13:40:03.926+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-25T00:00 to 2025-05-25T23:59:59.999999999 using time unit HOUR
2025-05-27T13:40:03.926+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-24T23:00 to 2025-05-26T00:59:59.999999999
2025-05-27T13:40:03.993+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 26 readings in extended date range
2025-05-27T13:40:03.994+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T13:40:03.994+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T13:40:03.994+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-24T23:00:00.301157 = 1531.142, Last reading: 2025-05-26T00:00:00.206583 = 1536.977
2025-05-27T13:40:03.994+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=2.3669434, 01:00 - 02:00=1.0090332, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.0059814453, 07:00 - 08:00=0.005004883, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0059814453, 10:00 - 11:00=0.005004883, 11:00 - 12:00=0.005004883, 12:00 - 13:00=0.0059814453, 13:00 - 14:00=0.005004883, 14:00 - 15:00=0.0059814453, 15:00 - 16:00=0.005004883, 16:00 - 17:00=0.0010986328, 17:00 - 18:00=0.0, 18:00 - 19:00=0.0, 19:00 - 20:00=0.0, 20:00 - 21:00=0.0, 21:00 - 22:00=0.0, 22:00 - 23:00=0.0, 23:00 - 00:00=0.0}
2025-05-27T13:40:03.995+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T13:40:03.995926800
2025-05-27T13:40:04.062+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T13:40:03.995926800 using time unit DAY
2025-05-27T13:40:04.063+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T13:40:04.124+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 662 readings in extended date range
2025-05-27T13:40:04.124+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T13:40:04.124+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T13:40:04.124+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T13:00:00.246831 = 1546.947
2025-05-27T13:40:04.124+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T13:40:04.124+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T13:40:04.203+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T13:40:04.203+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T13:40:04.266+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 4228 readings in extended date range
2025-05-27T13:40:04.266+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T13:40:04.282+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T13:40:04.282+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T13:00:00.246831 = 1546.947
2025-05-27T13:40:04.282+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T13:40:04.282+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T13:40:04.282+02:00  INFO 19520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T13:40:11.602+02:00  INFO 19520 --- [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-27T13:40:11.602+02:00  INFO 19520 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-27T13:40:11.602+02:00  INFO 19520 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T13:40:11.602+02:00  INFO 19520 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-05-27T13:40:11.903+02:00  INFO 19520 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-05-27T13:52:24.410+02:00  INFO 18208 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 18208 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-27T13:52:24.412+02:00  INFO 18208 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-27T13:52:24.448+02:00  INFO 18208 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-05-27T13:52:24.448+02:00  INFO 18208 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-05-27T13:52:25.074+02:00  INFO 18208 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-27T13:52:25.147+02:00  INFO 18208 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 67 ms. Found 10 JPA repository interfaces.
2025-05-27T13:52:25.692+02:00  INFO 18208 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-27T13:52:25.706+02:00  INFO 18208 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-27T13:52:25.706+02:00  INFO 18208 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-27T13:52:25.751+02:00  INFO 18208 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-27T13:52:25.751+02:00  INFO 18208 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1303 ms
2025-05-27T13:52:25.968+02:00  INFO 18208 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-27T13:52:26.017+02:00  INFO 18208 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.15.Final
2025-05-27T13:52:26.045+02:00  INFO 18208 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-27T13:52:26.288+02:00  INFO 18208 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-27T13:52:26.314+02:00  INFO 18208 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-05-27T13:52:27.134+02:00  INFO 18208 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@2a6c8c8a
2025-05-27T13:52:27.135+02:00  INFO 18208 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-05-27T13:52:27.419+02:00  INFO 18208 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-27T13:52:28.282+02:00  INFO 18208 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-27T13:52:30.548+02:00  INFO 18208 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T13:52:30.580+02:00  WARN 18208 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-27T13:52:30.785+02:00  INFO 18208 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-05-27T13:52:32.451+02:00  WARN 18208 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-27T13:52:32.481+02:00  INFO 18208 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-27T13:52:32.677+02:00  INFO 18208 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-27T13:52:33.097+02:00  INFO 18208 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-27T13:52:33.129+02:00  INFO 18208 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-27T13:52:33.136+02:00  INFO 18208 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 9.112 seconds (process running for 9.815)
2025-05-27T13:54:47.730+02:00  INFO 18208 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-27T13:54:47.730+02:00  INFO 18208 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-27T13:54:47.731+02:00  INFO 18208 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-05-27T13:54:51.252+02:00  INFO 18208 --- [Thread-8] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 13:54:51 CEST 2025
2025-05-27T13:54:51.253+02:00 ERROR 18208 --- [Thread-8] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@537a1d4f: Master is null.
2025-05-27T13:54:51.671+02:00  INFO 18208 --- [Thread-9] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 13:54:51 CEST 2025
2025-05-27T13:54:51.671+02:00 ERROR 18208 --- [Thread-9] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@4dbef09a: Master is null.
2025-05-27T13:54:51.730+02:00  INFO 18208 --- [Thread-9] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 13:54:51 CEST 2025
2025-05-27T13:54:51.730+02:00 ERROR 18208 --- [Thread-9] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@588e6179: Master is null.
2025-05-27T13:54:51.792+02:00  INFO 18208 --- [Thread-9] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 13:54:51 CEST 2025
2025-05-27T13:54:51.792+02:00 ERROR 18208 --- [Thread-9] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@5c022f6b: Master is null.
2025-05-27T13:54:51.852+02:00  INFO 18208 --- [Thread-9] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 13:54:51 CEST 2025
2025-05-27T13:54:51.852+02:00 ERROR 18208 --- [Thread-9] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@1d876f05: Master is null.
2025-05-27T13:54:52.138+02:00  INFO 18208 --- [Thread-10] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 13:54:52 CEST 2025
2025-05-27T13:54:52.138+02:00 ERROR 18208 --- [Thread-10] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@7719cb78: Master is null.
2025-05-27T13:54:52.505+02:00  INFO 18208 --- [Thread-11] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 13:54:52 CEST 2025
2025-05-27T13:54:52.505+02:00 ERROR 18208 --- [Thread-11] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@20a1c243: Master is null.
2025-05-27T13:54:52.565+02:00  INFO 18208 --- [Thread-11] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 13:54:52 CEST 2025
2025-05-27T13:54:52.565+02:00 ERROR 18208 --- [Thread-11] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@10dfe7fb: Master is null.
2025-05-27T13:54:52.626+02:00  INFO 18208 --- [Thread-11] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 13:54:52 CEST 2025
2025-05-27T13:54:52.626+02:00 ERROR 18208 --- [Thread-11] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@71527bd0: Master is null.
2025-05-27T13:54:52.687+02:00  INFO 18208 --- [Thread-11] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 13:54:52 CEST 2025
2025-05-27T13:54:52.687+02:00 ERROR 18208 --- [Thread-11] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@2d8e95a0: Master is null.
2025-05-27T13:54:52.998+02:00  INFO 18208 --- [Thread-12] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 13:54:52 CEST 2025
2025-05-27T13:54:52.999+02:00 ERROR 18208 --- [Thread-12] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@360cfcd8: Master is null.
2025-05-27T13:54:53.327+02:00  INFO 18208 --- [Thread-13] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 13:54:53 CEST 2025
2025-05-27T13:54:53.327+02:00 ERROR 18208 --- [Thread-13] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@6ededa43: Master is null.
2025-05-27T13:54:53.704+02:00  INFO 18208 --- [Thread-14] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 13:54:53 CEST 2025
2025-05-27T13:54:53.704+02:00 ERROR 18208 --- [Thread-14] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@6d86135e: Master is null.
2025-05-27T13:54:53.756+02:00  INFO 18208 --- [Thread-14] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 13:54:53 CEST 2025
2025-05-27T13:54:53.756+02:00 ERROR 18208 --- [Thread-14] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@66967642: Master is null.
2025-05-27T13:54:53.818+02:00  INFO 18208 --- [Thread-14] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 13:54:53 CEST 2025
2025-05-27T13:54:53.818+02:00 ERROR 18208 --- [Thread-14] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@5d16b0ef: Master is null.
2025-05-27T13:54:54.145+02:00  INFO 18208 --- [Thread-15] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 13:54:54 CEST 2025
2025-05-27T13:54:54.145+02:00 ERROR 18208 --- [Thread-15] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@514ef9cf: Master is null.
2025-05-27T13:54:54.435+02:00  INFO 18208 --- [Thread-16] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 13:54:54 CEST 2025
2025-05-27T13:54:54.435+02:00 ERROR 18208 --- [Thread-16] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@5c559cc5: Master is null.
2025-05-27T13:54:54.818+02:00  INFO 18208 --- [Thread-17] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 13:54:54 CEST 2025
2025-05-27T13:54:54.818+02:00 ERROR 18208 --- [Thread-17] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@6217b890: Master is null.
2025-05-27T13:54:56.683+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T13:54:56.683940300
2025-05-27T13:54:56.751+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T13:54:56.683940300 using time unit HOUR
2025-05-27T13:54:56.751+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T14:54:56.683940300
2025-05-27T13:54:56.817+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Found 15 readings in extended date range
2025-05-27T13:54:56.818+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Generated 14 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00]
2025-05-27T13:54:56.820+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 14 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00]
2025-05-27T13:54:56.820+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T13:00:00.246831 = 1546.947
2025-05-27T13:54:56.822+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0}
2025-05-27T13:54:56.824+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T13:54:56.824402
2025-05-27T13:54:56.891+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T13:54:56.824402 using time unit DAY
2025-05-27T13:54:56.891+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T13:54:56.959+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Found 662 readings in extended date range
2025-05-27T13:54:56.959+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T13:54:56.959+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T13:54:56.959+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T13:00:00.246831 = 1546.947
2025-05-27T13:54:56.962+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T13:54:56.972+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T13:54:57.049+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T13:54:57.049+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T13:54:57.125+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Found 4228 readings in extended date range
2025-05-27T13:54:57.125+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T13:54:57.127+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T13:54:57.127+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T13:00:00.246831 = 1546.947
2025-05-27T13:54:57.130+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T13:54:57.130+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T13:54:57.130+02:00  INFO 18208 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T13:55:07.539+02:00  INFO 18208 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999
2025-05-27T13:55:08.784+02:00  INFO 18208 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999 using time unit HOUR
2025-05-27T13:55:08.784+02:00  INFO 18208 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-25T23:00 to 2025-05-27T00:59:59.999999999
2025-05-27T13:55:08.850+02:00  INFO 18208 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Found 26 readings in extended date range
2025-05-27T13:55:08.850+02:00  INFO 18208 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Generated 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T13:55:08.851+02:00  INFO 18208 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T13:55:08.851+02:00  INFO 18208 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-25T23:00:00.203725 = 1536.977, Last reading: 2025-05-27T00:00:00.200409 = 1546.675
2025-05-27T13:55:08.852+02:00  INFO 18208 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.0, 01:00 - 02:00=0.0, 02:00 - 03:00=0.0, 03:00 - 04:00=0.0, 04:00 - 05:00=0.0, 05:00 - 06:00=0.0, 06:00 - 07:00=0.0, 07:00 - 08:00=0.0, 08:00 - 09:00=0.0, 09:00 - 10:00=0.0, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0, 15:00 - 16:00=0.0, 16:00 - 17:00=0.0, 17:00 - 18:00=0.0, 18:00 - 19:00=0.0, 19:00 - 20:00=0.3869629, 20:00 - 21:00=2.3790283, 21:00 - 22:00=2.3900146, 22:00 - 23:00=2.369995, 23:00 - 00:00=2.171997}
2025-05-27T13:55:19.072+02:00  INFO 18208 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-04-01T00:00 to 2025-04-30T23:59:59.999999999
2025-05-27T13:55:20.297+02:00  INFO 18208 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-04-01T00:00 to 2025-04-30T23:59:59.999999999 using time unit DAY
2025-05-27T13:55:20.297+02:00  INFO 18208 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-03-31T00:00 to 2025-05-01T23:59:59
2025-05-27T13:55:20.365+02:00  INFO 18208 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Found 755 readings in extended date range
2025-05-27T13:55:20.365+02:00  INFO 18208 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Generated 30 periods: [01/04, 02/04, 03/04, 04/04, 05/04, 06/04, 07/04, 08/04, 09/04, 10/04, 11/04, 12/04, 13/04, 14/04, 15/04, 16/04, 17/04, 18/04, 19/04, 20/04, 21/04, 22/04, 23/04, 24/04, 25/04, 26/04, 27/04, 28/04, 29/04, 30/04]
2025-05-27T13:55:20.365+02:00  INFO 18208 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 30 periods: [01/04, 02/04, 03/04, 04/04, 05/04, 06/04, 07/04, 08/04, 09/04, 10/04, 11/04, 12/04, 13/04, 14/04, 15/04, 16/04, 17/04, 18/04, 19/04, 20/04, 21/04, 22/04, 23/04, 24/04, 25/04, 26/04, 27/04, 28/04, 29/04, 30/04]
2025-05-27T13:55:20.365+02:00  INFO 18208 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-03-31T00:00:00.048771 = 1223.0, Last reading: 2025-05-01T23:00:00.048956 = 1402.753
2025-05-27T13:55:20.367+02:00  INFO 18208 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/04=9.3029785, 02/04=0.044921875, 03/04=10.106079, 04/04=0.0, 05/04=10.28894, 06/04=0.063964844, 07/04=10.23999, 08/04=0.06201172, 09/04=10.606079, 10/04=8.635986, 11/04=0.0, 12/04=10.42395, 13/04=0.123046875, 14/04=0.046020508, 15/04=10.25, 16/04=0.052978516, 17/04=0.0, 18/04=0.0, 19/04=10.243042, 20/04=12.033936, 21/04=7.2769775, 22/04=9.722046, 23/04=10.009033, 24/04=9.427002, 25/04=0.12207031, 26/04=9.983887, 27/04=9.242065, 28/04=0.043945312, 29/04=0.0, 30/04=0.0}
2025-05-27T13:55:25.133+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T13:55:25.133382800
2025-05-27T13:55:26.305+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T13:55:25.133382800 using time unit DAY
2025-05-27T13:55:26.305+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T13:55:26.372+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Found 662 readings in extended date range
2025-05-27T13:55:26.372+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T13:55:26.372+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T13:55:26.372+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T13:00:00.246831 = 1546.947
2025-05-27T13:55:26.374+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T13:55:30.743+02:00  INFO 18208 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2024-01-01T00:00 to 2024-12-31T23:59:59.999999999
2025-05-27T13:55:31.960+02:00  INFO 18208 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2024-01-01T00:00 to 2024-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T13:55:31.961+02:00  INFO 18208 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2023-12-01T00:00 to 2025-01-31T23:59:59
2025-05-27T13:55:32.041+02:00  INFO 18208 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Found 5439 readings in extended date range
2025-05-27T13:55:32.041+02:00  INFO 18208 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T13:55:32.041+02:00  INFO 18208 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T13:55:32.042+02:00  INFO 18208 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-06-13T18:00:00.262571 = 385.49, Last reading: 2025-01-31T23:00:00.047367 = 1027.671
2025-05-27T13:55:32.046+02:00  INFO 18208 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=0.0, Feb=0.0, Mar=0.0, Abr=0.0, May=0.0, Jun=49.399017, Jul=118.014984, Ago=69.617004, Sep=75.75897, Oct=98.82501, Nov=78.252014, Dic=75.276}
2025-05-27T13:55:32.046+02:00  INFO 18208 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T13:55:32.047+02:00  INFO 18208 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=0.0, Feb=0.0, Mar=0.0, Abr=0.0, May=0.0, Jun=49.399017, Jul=118.014984, Ago=69.617004, Sep=75.75897, Oct=98.82501, Nov=78.252014, Dic=75.276}
2025-05-27T13:55:33.832+02:00  INFO 18208 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T13:55:35.071+02:00  INFO 18208 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T13:55:35.071+02:00  INFO 18208 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T13:55:35.147+02:00  INFO 18208 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 4228 readings in extended date range
2025-05-27T13:55:35.147+02:00  INFO 18208 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T13:55:35.148+02:00  INFO 18208 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T13:55:35.148+02:00  INFO 18208 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T13:00:00.246831 = 1546.947
2025-05-27T13:55:35.152+02:00  INFO 18208 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T13:55:35.152+02:00  INFO 18208 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T13:55:35.152+02:00  INFO 18208 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T13:57:34.920+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-25T00:00 to 2025-05-25T23:59:59.999999999
2025-05-27T13:57:36.266+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-25T00:00 to 2025-05-25T23:59:59.999999999 using time unit HOUR
2025-05-27T13:57:36.266+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-24T23:00 to 2025-05-26T00:59:59.999999999
2025-05-27T13:57:36.330+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Found 26 readings in extended date range
2025-05-27T13:57:36.330+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Generated 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T13:57:36.334+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T13:57:36.334+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-24T23:00:00.301157 = 1531.142, Last reading: 2025-05-26T00:00:00.206583 = 1536.977
2025-05-27T13:57:36.335+02:00  INFO 18208 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=2.3669434, 01:00 - 02:00=1.0090332, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.0059814453, 07:00 - 08:00=0.005004883, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0059814453, 10:00 - 11:00=0.005004883, 11:00 - 12:00=0.005004883, 12:00 - 13:00=0.0059814453, 13:00 - 14:00=0.005004883, 14:00 - 15:00=0.0059814453, 15:00 - 16:00=0.005004883, 16:00 - 17:00=0.0010986328, 17:00 - 18:00=0.0, 18:00 - 19:00=0.0, 19:00 - 20:00=0.0, 20:00 - 21:00=0.0, 21:00 - 22:00=0.0, 22:00 - 23:00=0.0, 23:00 - 00:00=0.0}
2025-05-27T14:00:40.726+02:00  INFO 18208 --- [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 48 class path changes (0 additions, 0 deletions, 48 modifications)
2025-05-27T14:00:40.729+02:00  INFO 18208 --- [Thread-6] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-27T14:00:40.732+02:00  INFO 18208 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-27T14:00:40.737+02:00  INFO 18208 --- [Thread-6] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T14:00:40.739+02:00  INFO 18208 --- [Thread-6] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-05-27T14:00:41.032+02:00  INFO 18208 --- [Thread-6] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-05-27T14:00:41.119+02:00  INFO 18208 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 18208 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-27T14:00:41.119+02:00  INFO 18208 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-27T14:00:41.377+02:00  INFO 18208 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-27T14:00:41.412+02:00  INFO 18208 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 33 ms. Found 10 JPA repository interfaces.
2025-05-27T14:00:41.569+02:00  INFO 18208 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-27T14:00:41.571+02:00  INFO 18208 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-27T14:00:41.571+02:00  INFO 18208 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-27T14:00:41.594+02:00  INFO 18208 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-27T14:00:41.594+02:00  INFO 18208 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 473 ms
2025-05-27T14:00:41.677+02:00  INFO 18208 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-27T14:00:41.679+02:00  INFO 18208 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-27T14:00:41.689+02:00  INFO 18208 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-27T14:00:41.690+02:00  INFO 18208 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-05-27T14:00:42.311+02:00  INFO 18208 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@2ac4e4bc
2025-05-27T14:00:42.311+02:00  INFO 18208 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-05-27T14:00:42.550+02:00  INFO 18208 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-2)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-27T14:00:42.730+02:00  INFO 18208 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-27T14:00:44.873+02:00  INFO 18208 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T14:00:44.893+02:00  WARN 18208 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-27T14:00:45.240+02:00  WARN 18208 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-27T14:00:45.259+02:00  INFO 18208 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-27T14:00:45.377+02:00  INFO 18208 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-27T14:00:45.596+02:00  INFO 18208 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-27T14:00:45.608+02:00  INFO 18208 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-27T14:00:45.610+02:00  INFO 18208 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 4.528 seconds (process running for 502.289)
2025-05-27T14:00:45.940+02:00  INFO 18208 --- [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-05-27T14:01:21.842+02:00  INFO 18208 --- [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-27T14:01:21.845+02:00  INFO 18208 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-27T14:01:21.847+02:00  INFO 18208 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T14:01:21.847+02:00  INFO 18208 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-05-27T14:01:22.146+02:00  INFO 18208 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-05-27T14:01:23.925+02:00  INFO 15520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 15520 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-27T14:01:23.927+02:00  INFO 15520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-27T14:01:23.970+02:00  INFO 15520 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-05-27T14:01:23.971+02:00  INFO 15520 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-05-27T14:01:24.587+02:00  INFO 15520 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-27T14:01:24.662+02:00  INFO 15520 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 68 ms. Found 10 JPA repository interfaces.
2025-05-27T14:01:25.173+02:00  INFO 15520 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-27T14:01:25.184+02:00  INFO 15520 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-27T14:01:25.184+02:00  INFO 15520 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-27T14:01:25.221+02:00  INFO 15520 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-27T14:01:25.221+02:00  INFO 15520 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1250 ms
2025-05-27T14:01:25.395+02:00  INFO 15520 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-27T14:01:25.452+02:00  INFO 15520 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.15.Final
2025-05-27T14:01:25.479+02:00  INFO 15520 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-27T14:01:25.716+02:00  INFO 15520 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-27T14:01:25.746+02:00  INFO 15520 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-05-27T14:01:26.565+02:00  INFO 15520 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@5ee06231
2025-05-27T14:01:26.567+02:00  INFO 15520 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-05-27T14:01:26.871+02:00  INFO 15520 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-27T14:01:27.828+02:00  INFO 15520 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-27T14:01:30.093+02:00  INFO 15520 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T14:01:30.123+02:00  WARN 15520 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-27T14:01:30.328+02:00  INFO 15520 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-05-27T14:01:31.943+02:00  WARN 15520 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-27T14:01:31.974+02:00  INFO 15520 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-27T14:01:32.172+02:00  INFO 15520 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-27T14:01:32.622+02:00  INFO 15520 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-27T14:01:32.661+02:00  INFO 15520 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-27T14:01:32.667+02:00  INFO 15520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 9.125 seconds (process running for 9.866)
2025-05-27T14:01:42.740+02:00  INFO 15520 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-27T14:01:42.740+02:00  INFO 15520 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-27T14:01:42.741+02:00  INFO 15520 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-05-27T14:01:53.753+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:01:53.753663900
2025-05-27T14:01:53.832+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:01:53.753663900 using time unit HOUR
2025-05-27T14:01:53.832+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T15:01:53.753663900
2025-05-27T14:01:53.898+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Found 16 readings in extended date range
2025-05-27T14:01:53.898+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Generated 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:01:53.898+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:01:53.898+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:01:53.898+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0}
2025-05-27T14:01:53.898+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:01:53.898207800
2025-05-27T14:01:53.959+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:01:53.898207800 using time unit DAY
2025-05-27T14:01:53.959+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:01:54.042+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:01:54.042+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:01:54.042+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:01:54.043+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:01:54.044+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:01:54.053+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:01:54.126+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:01:54.126+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:01:54.201+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:01:54.202+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:01:54.202+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:01:54.203+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:01:54.207+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:01:54.207+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:01:54.207+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:01:59.938+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999
2025-05-27T14:02:01.157+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999 using time unit HOUR
2025-05-27T14:02:01.157+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-25T23:00 to 2025-05-27T00:59:59.999999999
2025-05-27T14:02:01.221+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Found 26 readings in extended date range
2025-05-27T14:02:01.221+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Generated 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T14:02:01.230+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T14:02:01.230+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-25T23:00:00.203725 = 1536.977, Last reading: 2025-05-27T00:00:00.200409 = 1546.675
2025-05-27T14:02:01.231+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.0, 01:00 - 02:00=0.0, 02:00 - 03:00=0.0, 03:00 - 04:00=0.0, 04:00 - 05:00=0.0, 05:00 - 06:00=0.0, 06:00 - 07:00=0.0, 07:00 - 08:00=0.0, 08:00 - 09:00=0.0, 09:00 - 10:00=0.0, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0, 15:00 - 16:00=0.0, 16:00 - 17:00=0.0, 17:00 - 18:00=0.0, 18:00 - 19:00=0.0, 19:00 - 20:00=0.3869629, 20:00 - 21:00=2.3790283, 21:00 - 22:00=2.3900146, 22:00 - 23:00=2.369995, 23:00 - 00:00=2.171997}
2025-05-27T14:02:03.307+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:02:03.307386300
2025-05-27T14:02:04.339+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:02:04.467+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:02:03.307386300 using time unit DAY
2025-05-27T14:02:04.468+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:02:04.531+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:02:04.531+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:02:04.531+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:02:04.531+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:02:04.536+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:02:04.984+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:02:04.984309400
2025-05-27T14:02:05.585+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:02:05.585+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:02:05.683+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:02:05.683+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:02:05.683+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:02:05.683+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:02:05.690+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:02:05.690+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:02:05.690+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:02:05.794+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999
2025-05-27T14:02:05.821+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:02:04.984309400 using time unit DAY
2025-05-27T14:02:05.828+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:02:05.894+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:02:05.894+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:02:05.894+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:02:05.894+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:02:05.894+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:02:07.015+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999 using time unit HOUR
2025-05-27T14:02:07.015+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-25T23:00 to 2025-05-27T00:59:59.999999999
2025-05-27T14:02:07.095+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Found 26 readings in extended date range
2025-05-27T14:02:07.095+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Generated 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T14:02:07.095+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T14:02:07.095+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-25T23:00:00.203725 = 1536.977, Last reading: 2025-05-27T00:00:00.200409 = 1546.675
2025-05-27T14:02:07.095+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.0, 01:00 - 02:00=0.0, 02:00 - 03:00=0.0, 03:00 - 04:00=0.0, 04:00 - 05:00=0.0, 05:00 - 06:00=0.0, 06:00 - 07:00=0.0, 07:00 - 08:00=0.0, 08:00 - 09:00=0.0, 09:00 - 10:00=0.0, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0, 15:00 - 16:00=0.0, 16:00 - 17:00=0.0, 17:00 - 18:00=0.0, 18:00 - 19:00=0.0, 19:00 - 20:00=0.3869629, 20:00 - 21:00=2.3790283, 21:00 - 22:00=2.3900146, 22:00 - 23:00=2.369995, 23:00 - 00:00=2.171997}
2025-05-27T14:02:09.477+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:02:10.694+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:02:10.694+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:02:10.773+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:02:10.773+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:02:10.773+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:02:10.773+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:02:10.773+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:02:10.773+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:02:10.773+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:02:13.835+02:00  INFO 15520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:02:13.835092200
2025-05-27T14:02:14.998+02:00  INFO 15520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:02:13.835092200 using time unit DAY
2025-05-27T14:02:14.998+02:00  INFO 15520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:02:15.062+02:00  INFO 15520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:02:15.062+02:00  INFO 15520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:02:15.062+02:00  INFO 15520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:02:15.062+02:00  INFO 15520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:02:15.062+02:00  INFO 15520 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:02:18.530+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:02:19.750+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:02:19.750+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:02:19.827+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:02:19.827+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:02:19.827+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:02:19.828+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:02:19.831+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:02:19.831+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:02:19.831+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:02:21.806+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:02:21.806956600
2025-05-27T14:02:22.951+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:02:21.806956600 using time unit DAY
2025-05-27T14:02:22.951+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:02:23.017+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:02:23.017+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:02:23.017+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:02:23.017+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:02:23.018+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:02:27.221+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999
2025-05-27T14:02:28.429+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999 using time unit HOUR
2025-05-27T14:02:28.429+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-25T23:00 to 2025-05-27T00:59:59.999999999
2025-05-27T14:02:28.495+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Found 26 readings in extended date range
2025-05-27T14:02:28.495+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Generated 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T14:02:28.496+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T14:02:28.496+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-25T23:00:00.203725 = 1536.977, Last reading: 2025-05-27T00:00:00.200409 = 1546.675
2025-05-27T14:02:28.496+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.0, 01:00 - 02:00=0.0, 02:00 - 03:00=0.0, 03:00 - 04:00=0.0, 04:00 - 05:00=0.0, 05:00 - 06:00=0.0, 06:00 - 07:00=0.0, 07:00 - 08:00=0.0, 08:00 - 09:00=0.0, 09:00 - 10:00=0.0, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0, 15:00 - 16:00=0.0, 16:00 - 17:00=0.0, 17:00 - 18:00=0.0, 18:00 - 19:00=0.0, 19:00 - 20:00=0.3869629, 20:00 - 21:00=2.3790283, 21:00 - 22:00=2.3900146, 22:00 - 23:00=2.369995, 23:00 - 00:00=2.171997}
2025-05-27T14:02:30.685+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:02:30.685790400
2025-05-27T14:02:31.876+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:02:30.685790400 using time unit HOUR
2025-05-27T14:02:31.876+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T15:02:30.685790400
2025-05-27T14:02:31.942+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Found 16 readings in extended date range
2025-05-27T14:02:31.942+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Generated 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:02:31.942+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:02:31.942+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:02:31.942+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0}
2025-05-27T14:02:34.107+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:02:34.107231800
2025-05-27T14:02:35.284+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:02:34.107231800 using time unit HOUR
2025-05-27T14:02:35.284+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T15:02:34.107231800
2025-05-27T14:02:35.349+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Found 16 readings in extended date range
2025-05-27T14:02:35.350+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Generated 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:02:35.350+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:02:35.350+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:02:35.350+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0}
2025-05-27T14:03:28.188+02:00  INFO 15520 --- [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 47 class path changes (0 additions, 0 deletions, 47 modifications)
2025-05-27T14:03:28.191+02:00  INFO 15520 --- [Thread-6] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-27T14:03:28.194+02:00  INFO 15520 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-27T14:03:28.199+02:00  INFO 15520 --- [Thread-6] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T14:03:28.202+02:00  INFO 15520 --- [Thread-6] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-05-27T14:03:28.493+02:00  INFO 15520 --- [Thread-6] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-05-27T14:03:28.563+02:00  INFO 15520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 15520 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-27T14:03:28.563+02:00  INFO 15520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-27T14:03:28.802+02:00  INFO 15520 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-27T14:03:28.835+02:00  INFO 15520 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 32 ms. Found 10 JPA repository interfaces.
2025-05-27T14:03:28.950+02:00  INFO 15520 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-27T14:03:28.950+02:00  INFO 15520 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-27T14:03:28.950+02:00  INFO 15520 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-27T14:03:28.969+02:00  INFO 15520 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-27T14:03:28.969+02:00  INFO 15520 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 404 ms
2025-05-27T14:03:29.044+02:00  INFO 15520 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-27T14:03:29.046+02:00  INFO 15520 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-27T14:03:29.053+02:00  INFO 15520 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-27T14:03:29.054+02:00  INFO 15520 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-05-27T14:03:29.656+02:00  INFO 15520 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@66547428
2025-05-27T14:03:29.656+02:00  INFO 15520 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-05-27T14:03:29.896+02:00  INFO 15520 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-2)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-27T14:03:30.049+02:00  INFO 15520 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-27T14:03:32.194+02:00  INFO 15520 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T14:03:32.208+02:00  WARN 15520 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-27T14:03:32.574+02:00  WARN 15520 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-27T14:03:32.594+02:00  INFO 15520 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-27T14:03:32.733+02:00  INFO 15520 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-27T14:03:32.957+02:00  INFO 15520 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-27T14:03:32.957+02:00  INFO 15520 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-27T14:03:32.971+02:00  INFO 15520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 4.433 seconds (process running for 130.172)
2025-05-27T14:03:33.289+02:00  INFO 15520 --- [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-05-27T14:05:06.715+02:00  INFO 15520 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-27T14:05:06.715+02:00  INFO 15520 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-27T14:05:06.716+02:00  INFO 15520 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-05-27T14:05:06.718+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:05:06.718739400
2025-05-27T14:05:07.977+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:05:06.718739400 using time unit HOUR
2025-05-27T14:05:07.977+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T15:05:06.718739400
2025-05-27T14:05:08.043+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 16 readings in extended date range
2025-05-27T14:05:08.044+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:05:08.045+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:05:08.045+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:05:08.046+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0}
2025-05-27T14:05:34.078+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:05:34.078136800
2025-05-27T14:05:34.141+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:05:34.078136800 using time unit HOUR
2025-05-27T14:05:34.141+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T15:05:34.078136800
2025-05-27T14:05:34.218+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Found 16 readings in extended date range
2025-05-27T14:05:34.218+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Generated 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:05:34.218+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:05:34.218+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:05:34.219+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0}
2025-05-27T14:05:34.219+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:05:34.219047900
2025-05-27T14:05:34.286+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:05:34.219047900 using time unit DAY
2025-05-27T14:05:34.286+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:05:34.336+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:05:34.352+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:05:34.352+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:05:34.352+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:05:34.352+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:05:34.352+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:05:34.423+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:05:34.423+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:05:34.499+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:05:34.499+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:05:34.499+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:05:34.499+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:05:34.499+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:05:34.499+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:05:34.499+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:05:38.139+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:05:38.139703900
2025-05-27T14:05:39.304+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:05:38.139703900 using time unit DAY
2025-05-27T14:05:39.304+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:05:39.367+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:05:39.367+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:05:39.367+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:05:39.367+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:05:39.367+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:05:40.446+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:05:41.394+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:05:41.394+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:05:41.458+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:05:41.458+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:05:41.458+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:05:41.458+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:05:41.474+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:05:41.474+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:05:41.474+02:00  INFO 15520 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:05:42.336+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:05:42.336156300
2025-05-27T14:05:43.246+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:05:42.336156300 using time unit HOUR
2025-05-27T14:05:43.246+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T15:05:42.336156300
2025-05-27T14:05:43.312+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Found 16 readings in extended date range
2025-05-27T14:05:43.312+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Generated 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:05:43.312+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:05:43.312+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:05:43.312+02:00  INFO 15520 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0}
2025-05-27T14:06:05.925+02:00  INFO 15520 --- [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 47 class path changes (0 additions, 0 deletions, 47 modifications)
2025-05-27T14:06:05.928+02:00  INFO 15520 --- [Thread-8] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-27T14:06:06.493+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:06:06.493722900
2025-05-27T14:06:06.559+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:06:06.493722900 using time unit HOUR
2025-05-27T14:06:06.560+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T15:06:06.493722900
2025-05-27T14:06:06.625+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Found 16 readings in extended date range
2025-05-27T14:06:06.625+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Generated 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:06:06.625+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:06:06.625+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:06:06.625+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0}
2025-05-27T14:06:06.625+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:06:06.625600500
2025-05-27T14:06:06.691+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:06:06.625600500 using time unit DAY
2025-05-27T14:06:06.691+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:06:06.758+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:06:06.758+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:06:06.758+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:06:06.758+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:06:06.759+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:06:06.760+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:06:06.833+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:06:06.833+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:06:06.906+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:06:06.906+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:06:06.906+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:06:06.907+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:06:06.910+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:06:06.910+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:06:06.910+02:00  INFO 15520 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:06:07.359+02:00  INFO 15520 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-27T14:06:07.361+02:00  INFO 15520 --- [Thread-8] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T14:06:07.362+02:00  INFO 15520 --- [Thread-8] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-05-27T14:06:07.654+02:00  INFO 15520 --- [Thread-8] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-05-27T14:06:07.725+02:00  INFO 15520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 15520 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-27T14:06:07.726+02:00  INFO 15520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-27T14:06:07.937+02:00  INFO 15520 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-27T14:06:07.968+02:00  INFO 15520 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 30 ms. Found 10 JPA repository interfaces.
2025-05-27T14:06:08.080+02:00  INFO 15520 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-27T14:06:08.081+02:00  INFO 15520 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-27T14:06:08.081+02:00  INFO 15520 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-27T14:06:08.098+02:00  INFO 15520 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-27T14:06:08.098+02:00  INFO 15520 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 371 ms
2025-05-27T14:06:08.175+02:00  INFO 15520 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-27T14:06:08.178+02:00  INFO 15520 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-27T14:06:08.184+02:00  INFO 15520 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-27T14:06:08.185+02:00  INFO 15520 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-3 - Starting...
2025-05-27T14:06:08.795+02:00  INFO 15520 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-3 - Added connection org.postgresql.jdbc.PgConnection@33dc9591
2025-05-27T14:06:08.795+02:00  INFO 15520 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-3 - Start completed.
2025-05-27T14:06:09.034+02:00  INFO 15520 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-3)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-27T14:06:09.174+02:00  INFO 15520 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-27T14:06:11.319+02:00  INFO 15520 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T14:06:11.340+02:00  WARN 15520 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-27T14:06:11.725+02:00  WARN 15520 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-27T14:06:11.736+02:00  INFO 15520 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-27T14:06:11.863+02:00  INFO 15520 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-27T14:06:12.100+02:00  INFO 15520 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-27T14:06:12.116+02:00  INFO 15520 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-27T14:06:12.116+02:00  INFO 15520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 4.413 seconds (process running for 289.316)
2025-05-27T14:06:12.480+02:00  INFO 15520 --- [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-05-27T14:06:13.221+02:00  INFO 15520 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-27T14:06:13.221+02:00  INFO 15520 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-27T14:06:13.223+02:00  INFO 15520 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-05-27T14:06:13.223+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:06:14.115+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:06:14.115183500
2025-05-27T14:06:14.492+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:06:14.492+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:06:14.556+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:06:14.571+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:06:14.573+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:06:14.573+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:06:14.573+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:06:14.573+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:06:14.573+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:06:15.179+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:06:15.179418
2025-05-27T14:06:15.357+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:06:14.115183500 using time unit HOUR
2025-05-27T14:06:15.357+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T15:06:14.115183500
2025-05-27T14:06:15.416+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Found 16 readings in extended date range
2025-05-27T14:06:15.423+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Generated 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:06:15.423+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:06:15.423+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:06:15.423+02:00  INFO 15520 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0}
2025-05-27T14:06:16.024+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:06:15.179418 using time unit DAY
2025-05-27T14:06:16.024+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:06:16.090+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:06:16.090+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:06:16.090+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:06:16.090+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:06:16.091+02:00  INFO 15520 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:06:17.459+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:06:18.394+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:06:18.394+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:06:18.468+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:06:18.468+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:06:18.469+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:06:18.469+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:06:18.472+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:06:18.472+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:06:18.472+02:00  INFO 15520 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:06:42.938+02:00  INFO 15520 --- [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 47 class path changes (0 additions, 0 deletions, 47 modifications)
2025-05-27T14:06:42.939+02:00  INFO 15520 --- [Thread-12] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-27T14:06:42.942+02:00  INFO 15520 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-27T14:06:42.944+02:00  INFO 15520 --- [Thread-12] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T14:06:42.945+02:00  INFO 15520 --- [Thread-12] com.zaxxer.hikari.HikariDataSource       : HikariPool-3 - Shutdown initiated...
2025-05-27T14:06:43.237+02:00  INFO 15520 --- [Thread-12] com.zaxxer.hikari.HikariDataSource       : HikariPool-3 - Shutdown completed.
2025-05-27T14:06:43.311+02:00  INFO 15520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 15520 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-27T14:06:43.311+02:00  INFO 15520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-27T14:06:43.522+02:00  INFO 15520 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-27T14:06:43.554+02:00  INFO 15520 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 30 ms. Found 10 JPA repository interfaces.
2025-05-27T14:06:43.666+02:00  INFO 15520 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-27T14:06:43.667+02:00  INFO 15520 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-27T14:06:43.667+02:00  INFO 15520 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-27T14:06:43.684+02:00  INFO 15520 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-27T14:06:43.685+02:00  INFO 15520 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 372 ms
2025-05-27T14:06:43.763+02:00  INFO 15520 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-27T14:06:43.766+02:00  INFO 15520 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-27T14:06:43.773+02:00  INFO 15520 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-27T14:06:43.774+02:00  INFO 15520 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-4 - Starting...
2025-05-27T14:06:44.380+02:00  INFO 15520 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-4 - Added connection org.postgresql.jdbc.PgConnection@cf32300
2025-05-27T14:06:44.380+02:00  INFO 15520 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-4 - Start completed.
2025-05-27T14:06:44.617+02:00  INFO 15520 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-4)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-27T14:06:44.726+02:00  INFO 15520 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-27T14:06:46.852+02:00  INFO 15520 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T14:06:46.884+02:00  WARN 15520 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-27T14:06:47.228+02:00  WARN 15520 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-27T14:06:47.249+02:00  INFO 15520 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-27T14:06:47.375+02:00  INFO 15520 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-27T14:06:47.631+02:00  INFO 15520 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-27T14:06:47.643+02:00  INFO 15520 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-27T14:06:47.645+02:00  INFO 15520 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 4.356 seconds (process running for 324.843)
2025-05-27T14:06:47.645+02:00  INFO 15520 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-27T14:06:47.646+02:00  INFO 15520 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-27T14:06:47.646+02:00  INFO 15520 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 0 ms
2025-05-27T14:06:47.968+02:00  INFO 15520 --- [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-05-27T14:06:49.948+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:06:49.948645500
2025-05-27T14:06:50.015+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:06:49.948645500 using time unit HOUR
2025-05-27T14:06:50.015+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T15:06:49.948645500
2025-05-27T14:06:50.076+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 16 readings in extended date range
2025-05-27T14:06:50.082+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:06:50.082+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:06:50.082+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:06:50.082+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0}
2025-05-27T14:06:50.082+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:06:50.082973100
2025-05-27T14:06:50.152+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:06:50.082973100 using time unit DAY
2025-05-27T14:06:50.152+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:06:50.221+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:06:50.221+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:06:50.221+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:06:50.221+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:06:50.221+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:06:50.221+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:06:50.291+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:06:50.291+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:06:50.370+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:06:50.370+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:06:50.370+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:06:50.370+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:06:50.375+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:06:50.375+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:06:50.375+02:00  INFO 15520 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:06:59.829+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:06:59.829272400
2025-05-27T14:07:00.982+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:06:59.829272400 using time unit DAY
2025-05-27T14:07:00.982+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:07:01.062+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:07:01.062+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:07:01.062+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:07:01.062+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:07:01.062+02:00  INFO 15520 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:07:30.345+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:07:31.580+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:07:31.580+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:07:31.651+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:07:31.651+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:07:31.651+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:07:31.651+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:07:31.660+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:07:31.660+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:07:31.660+02:00  INFO 15520 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:34:43.478+02:00  INFO 15520 --- [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-27T14:34:43.478+02:00  INFO 15520 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-27T14:34:43.494+02:00  INFO 15520 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T14:34:43.494+02:00  INFO 15520 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-4 - Shutdown initiated...
2025-05-27T14:34:43.840+02:00  INFO 15520 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-4 - Shutdown completed.
2025-05-27T14:34:46.819+02:00  INFO 15560 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 15560 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-27T14:34:46.821+02:00  INFO 15560 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-27T14:34:46.859+02:00  INFO 15560 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-05-27T14:34:46.859+02:00  INFO 15560 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-05-27T14:34:47.524+02:00  INFO 15560 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-27T14:34:47.610+02:00  INFO 15560 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 78 ms. Found 10 JPA repository interfaces.
2025-05-27T14:34:48.153+02:00  INFO 15560 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-27T14:34:48.165+02:00  INFO 15560 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-27T14:34:48.166+02:00  INFO 15560 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-27T14:34:48.204+02:00  INFO 15560 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-27T14:34:48.205+02:00  INFO 15560 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1346 ms
2025-05-27T14:34:48.399+02:00  INFO 15560 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-27T14:34:48.460+02:00  INFO 15560 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.15.Final
2025-05-27T14:34:48.491+02:00  INFO 15560 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-27T14:34:48.762+02:00  INFO 15560 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-27T14:34:48.788+02:00  INFO 15560 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-05-27T14:34:49.614+02:00  INFO 15560 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@3c6ab605
2025-05-27T14:34:49.616+02:00  INFO 15560 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-05-27T14:34:49.903+02:00  INFO 15560 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-27T14:34:50.814+02:00  INFO 15560 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-27T14:34:53.200+02:00  INFO 15560 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T14:34:53.263+02:00  WARN 15560 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-27T14:34:53.632+02:00  INFO 15560 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-05-27T14:34:55.471+02:00  WARN 15560 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-27T14:34:55.505+02:00  INFO 15560 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-27T14:34:55.723+02:00  INFO 15560 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-27T14:34:56.181+02:00  INFO 15560 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-27T14:34:56.222+02:00  INFO 15560 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-27T14:34:56.229+02:00  INFO 15560 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 9.805 seconds (process running for 10.463)
2025-05-27T14:34:57.568+02:00  INFO 15560 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-27T14:34:57.568+02:00  INFO 15560 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-27T14:34:57.574+02:00  INFO 15560 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 6 ms
2025-05-27T14:35:01.144+02:00  INFO 15560 --- [Thread-8] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 14:35:01 CEST 2025
2025-05-27T14:35:01.144+02:00 ERROR 15560 --- [Thread-8] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@1bf09d7c: Master is null.
2025-05-27T14:35:01.537+02:00  INFO 15560 --- [Thread-9] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 14:35:01 CEST 2025
2025-05-27T14:35:01.537+02:00 ERROR 15560 --- [Thread-9] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@586b6f7c: Master is null.
2025-05-27T14:35:01.614+02:00  INFO 15560 --- [Thread-9] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 14:35:01 CEST 2025
2025-05-27T14:35:01.614+02:00 ERROR 15560 --- [Thread-9] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@3a334a5c: Master is null.
2025-05-27T14:35:01.671+02:00  INFO 15560 --- [Thread-9] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 14:35:01 CEST 2025
2025-05-27T14:35:01.671+02:00 ERROR 15560 --- [Thread-9] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@431fbd39: Master is null.
2025-05-27T14:35:01.729+02:00  INFO 15560 --- [Thread-9] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 14:35:01 CEST 2025
2025-05-27T14:35:01.729+02:00 ERROR 15560 --- [Thread-9] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@4bcf84a8: Master is null.
2025-05-27T14:35:02.017+02:00  INFO 15560 --- [Thread-10] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 14:35:02 CEST 2025
2025-05-27T14:35:02.018+02:00 ERROR 15560 --- [Thread-10] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@18589f9c: Master is null.
2025-05-27T14:35:02.393+02:00  INFO 15560 --- [Thread-11] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 14:35:02 CEST 2025
2025-05-27T14:35:02.393+02:00 ERROR 15560 --- [Thread-11] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@9cb39db: Master is null.
2025-05-27T14:35:02.456+02:00  INFO 15560 --- [Thread-11] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 14:35:02 CEST 2025
2025-05-27T14:35:02.456+02:00 ERROR 15560 --- [Thread-11] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@7e9030af: Master is null.
2025-05-27T14:35:02.508+02:00  INFO 15560 --- [Thread-11] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 14:35:02 CEST 2025
2025-05-27T14:35:02.508+02:00 ERROR 15560 --- [Thread-11] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@596dba0e: Master is null.
2025-05-27T14:35:02.560+02:00  INFO 15560 --- [Thread-11] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 14:35:02 CEST 2025
2025-05-27T14:35:02.560+02:00 ERROR 15560 --- [Thread-11] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@223fbb29: Master is null.
2025-05-27T14:35:02.805+02:00  INFO 15560 --- [Thread-12] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 14:35:02 CEST 2025
2025-05-27T14:35:02.805+02:00 ERROR 15560 --- [Thread-12] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@4a6be7d6: Master is null.
2025-05-27T14:35:03.182+02:00  INFO 15560 --- [Thread-13] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 14:35:03 CEST 2025
2025-05-27T14:35:03.182+02:00 ERROR 15560 --- [Thread-13] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@1d7d5400: Master is null.
2025-05-27T14:35:03.525+02:00  INFO 15560 --- [Thread-14] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 14:35:03 CEST 2025
2025-05-27T14:35:03.532+02:00 ERROR 15560 --- [Thread-14] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@30d16789: Master is null.
2025-05-27T14:35:03.583+02:00  INFO 15560 --- [Thread-14] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 14:35:03 CEST 2025
2025-05-27T14:35:03.583+02:00 ERROR 15560 --- [Thread-14] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@77fc745e: Master is null.
2025-05-27T14:35:03.642+02:00  INFO 15560 --- [Thread-15] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 14:35:03 CEST 2025
2025-05-27T14:35:03.643+02:00 ERROR 15560 --- [Thread-15] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@329f155b: Master is null.
2025-05-27T14:35:03.916+02:00  INFO 15560 --- [Thread-16] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 14:35:03 CEST 2025
2025-05-27T14:35:03.917+02:00 ERROR 15560 --- [Thread-16] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@2114e341: Master is null.
2025-05-27T14:35:04.304+02:00  INFO 15560 --- [Thread-17] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 14:35:04 CEST 2025
2025-05-27T14:35:04.305+02:00 ERROR 15560 --- [Thread-17] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@47a0174f: Master is null.
2025-05-27T14:35:04.779+02:00  INFO 15560 --- [Thread-18] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 14:35:04 CEST 2025
2025-05-27T14:35:04.780+02:00 ERROR 15560 --- [Thread-18] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@2627ebbf: Master is null.
2025-05-27T14:35:07.878+02:00  INFO 15560 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:35:07.878263100
2025-05-27T14:35:07.942+02:00  INFO 15560 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:35:07.878263100 using time unit HOUR
2025-05-27T14:35:07.942+02:00  INFO 15560 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T15:35:07.878263100
2025-05-27T14:35:08.005+02:00  INFO 15560 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Found 16 readings in extended date range
2025-05-27T14:35:08.005+02:00  INFO 15560 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Generated 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:35:08.021+02:00  INFO 15560 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:35:08.021+02:00  INFO 15560 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:35:08.021+02:00  INFO 15560 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0}
2025-05-27T14:35:08.021+02:00  INFO 15560 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:35:08.021597200
2025-05-27T14:35:08.085+02:00  INFO 15560 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:35:08.021597200 using time unit DAY
2025-05-27T14:35:08.085+02:00  INFO 15560 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:35:08.161+02:00  INFO 15560 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:35:08.161+02:00  INFO 15560 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:35:08.161+02:00  INFO 15560 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:35:08.161+02:00  INFO 15560 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:35:08.161+02:00  INFO 15560 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:35:08.164+02:00  INFO 15560 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:35:08.243+02:00  INFO 15560 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:35:08.243+02:00  INFO 15560 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:35:08.322+02:00  INFO 15560 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:35:08.322+02:00  INFO 15560 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:35:08.322+02:00  INFO 15560 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:35:08.322+02:00  INFO 15560 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:35:08.322+02:00  INFO 15560 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:35:08.322+02:00  INFO 15560 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:35:08.322+02:00  INFO 15560 --- [http-nio-8080-exec-8] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:35:13.400+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:35:13.400176400
2025-05-27T14:35:14.617+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:35:13.400176400 using time unit DAY
2025-05-27T14:35:14.617+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:35:14.685+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:35:14.685+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:35:14.685+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:35:14.685+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:35:14.686+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:35:14.818+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:35:14.818557600
2025-05-27T14:35:14.885+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:35:14.818557600 using time unit HOUR
2025-05-27T14:35:14.885+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T15:35:14.818557600
2025-05-27T14:35:14.889+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:35:14.949+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Found 16 readings in extended date range
2025-05-27T14:35:14.949+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Generated 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:35:14.949+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:35:14.949+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:35:14.949+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0}
2025-05-27T14:35:14.949+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:35:14.949129500
2025-05-27T14:35:15.018+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:35:14.949129500 using time unit DAY
2025-05-27T14:35:15.018+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:35:15.081+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:35:15.081+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:35:15.081+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:35:15.081+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:35:15.081+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:35:15.081+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:35:15.165+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:35:15.165+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:35:15.242+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:35:15.242+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:35:15.242+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:35:15.242+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:35:15.242+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:35:15.242+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:35:15.242+02:00  INFO 15560 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:35:16.066+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:35:16.066123700
2025-05-27T14:35:16.280+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:35:16.280+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:35:16.359+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:35:16.359+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:35:16.359+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:35:16.359+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:35:16.359+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:35:16.359+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:35:16.359+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:35:16.502+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:35:16.502237600
2025-05-27T14:35:16.580+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:35:16.502237600 using time unit HOUR
2025-05-27T14:35:16.580+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T15:35:16.502237600
2025-05-27T14:35:16.643+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Found 16 readings in extended date range
2025-05-27T14:35:16.643+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Generated 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:35:16.643+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:35:16.643+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:35:16.643+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0}
2025-05-27T14:35:16.643+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:35:16.643967900
2025-05-27T14:35:16.707+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:35:16.643967900 using time unit DAY
2025-05-27T14:35:16.707+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:35:16.772+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:35:16.772+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:35:16.772+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:35:16.772+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:35:16.772+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:35:16.772+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:35:16.853+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:35:16.853+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:35:16.928+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:35:16.928+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:35:16.928+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:35:16.928+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:35:16.935+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:35:16.935+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:35:16.935+02:00  INFO 15560 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:35:17.195+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:35:16.066123700 using time unit DAY
2025-05-27T14:35:17.195+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:35:17.254+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:35:17.254+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:35:17.254+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:35:17.254+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:35:17.254+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:35:17.381+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:35:17.381289500
2025-05-27T14:35:17.460+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:35:17.381289500 using time unit HOUR
2025-05-27T14:35:17.460+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T15:35:17.381289500
2025-05-27T14:35:17.524+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Found 16 readings in extended date range
2025-05-27T14:35:17.524+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Generated 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:35:17.524+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:35:17.524+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:35:17.524+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0}
2025-05-27T14:35:17.524+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:35:17.524508600
2025-05-27T14:35:17.587+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:35:17.524508600 using time unit DAY
2025-05-27T14:35:17.587+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:35:17.651+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:35:17.651+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:35:17.651+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:35:17.651+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:35:17.651+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:35:17.651+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:35:17.730+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:35:17.730+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:35:17.808+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:35:17.808+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:35:17.808+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:35:17.826+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:35:17.834+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:35:17.834+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:35:17.834+02:00  INFO 15560 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:35:19.240+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:35:20.461+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:35:20.461+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:35:20.541+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:35:20.541+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:35:20.541+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:35:20.541+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:35:20.541+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:35:20.541+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:35:20.541+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:35:20.684+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:35:20.684359900
2025-05-27T14:35:20.747+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:35:20.684359900 using time unit HOUR
2025-05-27T14:35:20.747+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T15:35:20.684359900
2025-05-27T14:35:20.811+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 16 readings in extended date range
2025-05-27T14:35:20.811+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:35:20.811+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:35:20.811+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:35:20.811+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0}
2025-05-27T14:35:20.811+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:35:20.811015600
2025-05-27T14:35:20.874+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:35:20.811015600 using time unit DAY
2025-05-27T14:35:20.874+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:35:20.938+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:35:20.938+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:35:20.938+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:35:20.954+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:35:20.954+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:35:20.954+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:35:21.018+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:35:21.018+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:35:21.099+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:35:21.099+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:35:21.099+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:35:21.099+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:35:21.099+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:35:21.099+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:35:21.099+02:00  INFO 15560 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:35:30.624+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:35:30.624589600
2025-05-27T14:35:31.852+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:35:30.624589600 using time unit DAY
2025-05-27T14:35:31.852+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:35:31.918+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:35:31.918+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:35:31.918+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:35:31.919+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:35:31.920+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:35:32.047+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:35:32.047101700
2025-05-27T14:35:32.116+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:35:32.047101700 using time unit HOUR
2025-05-27T14:35:32.116+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T15:35:32.047101700
2025-05-27T14:35:32.179+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Found 16 readings in extended date range
2025-05-27T14:35:32.179+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Generated 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:35:32.179+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:35:32.179+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:35:32.179+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0}
2025-05-27T14:35:32.179+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:35:32.179171300
2025-05-27T14:35:32.248+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:35:32.179171300 using time unit DAY
2025-05-27T14:35:32.248+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:35:32.317+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:35:32.317+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:35:32.317+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:35:32.317+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:35:32.317+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:35:32.317+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:35:32.393+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:35:32.393+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:35:32.467+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:35:32.468+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:35:32.468+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:35:32.468+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:35:32.469+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:35:32.469+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:35:32.469+02:00  INFO 15560 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:35:40.748+02:00  INFO 15560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:35:40.748751100
2025-05-27T14:35:40.812+02:00  INFO 15560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:35:40.748751100 using time unit HOUR
2025-05-27T14:35:40.812+02:00  INFO 15560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T15:35:40.748751100
2025-05-27T14:35:40.875+02:00  INFO 15560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Found 16 readings in extended date range
2025-05-27T14:35:40.875+02:00  INFO 15560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Generated 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:35:40.875+02:00  INFO 15560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:35:40.875+02:00  INFO 15560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:35:40.875+02:00  INFO 15560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0}
2025-05-27T14:35:40.875+02:00  INFO 15560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:35:40.875961200
2025-05-27T14:35:40.939+02:00  INFO 15560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:35:40.875961200 using time unit DAY
2025-05-27T14:35:40.939+02:00  INFO 15560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:35:41.018+02:00  INFO 15560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:35:41.018+02:00  INFO 15560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:35:41.018+02:00  INFO 15560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:35:41.018+02:00  INFO 15560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:35:41.018+02:00  INFO 15560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:35:41.018+02:00  INFO 15560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:35:41.081+02:00  INFO 15560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:35:41.081+02:00  INFO 15560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:35:41.161+02:00  INFO 15560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:35:41.161+02:00  INFO 15560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:35:41.161+02:00  INFO 15560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:35:41.161+02:00  INFO 15560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:35:41.161+02:00  INFO 15560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:35:41.161+02:00  INFO 15560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:35:41.161+02:00  INFO 15560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:35:49.915+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:35:49.915256100
2025-05-27T14:35:51.138+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:35:49.915256100 using time unit DAY
2025-05-27T14:35:51.138+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:35:51.211+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:35:51.211+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:35:51.211+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:35:51.211+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:35:51.211+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:35:51.328+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:35:51.328253
2025-05-27T14:35:51.407+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:35:51.328253 using time unit HOUR
2025-05-27T14:35:51.407+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T15:35:51.328253
2025-05-27T14:35:51.471+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Found 16 readings in extended date range
2025-05-27T14:35:51.471+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Generated 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:35:51.471+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:35:51.471+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:35:51.471+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0}
2025-05-27T14:35:51.471+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:35:51.471138700
2025-05-27T14:35:51.535+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:35:51.471138700 using time unit DAY
2025-05-27T14:35:51.535+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:35:51.598+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:35:51.598+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:35:51.598+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:35:51.598+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:35:51.598+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:35:51.598+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:35:51.683+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:35:51.683+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:35:51.757+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:35:51.757+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:35:51.757+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:35:51.757+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:35:51.757+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:35:51.757+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:35:51.757+02:00  INFO 15560 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:39:46.146+02:00  INFO 15560 --- [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-27T14:39:46.150+02:00  INFO 15560 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-27T14:39:46.155+02:00  INFO 15560 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T14:39:46.157+02:00  INFO 15560 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-05-27T14:39:46.466+02:00  INFO 15560 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-05-27T14:39:48.843+02:00  INFO 9732 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 9732 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-27T14:39:48.845+02:00  INFO 9732 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-27T14:39:48.880+02:00  INFO 9732 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-05-27T14:39:48.881+02:00  INFO 9732 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-05-27T14:39:49.510+02:00  INFO 9732 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-27T14:39:49.585+02:00  INFO 9732 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 69 ms. Found 10 JPA repository interfaces.
2025-05-27T14:39:50.088+02:00  INFO 9732 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-27T14:39:50.098+02:00  INFO 9732 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-27T14:39:50.098+02:00  INFO 9732 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-27T14:39:50.137+02:00  INFO 9732 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-27T14:39:50.138+02:00  INFO 9732 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1257 ms
2025-05-27T14:39:50.320+02:00  INFO 9732 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-27T14:39:50.369+02:00  INFO 9732 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.15.Final
2025-05-27T14:39:50.396+02:00  INFO 9732 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-27T14:39:50.625+02:00  INFO 9732 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-27T14:39:50.651+02:00  INFO 9732 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-05-27T14:39:51.463+02:00  INFO 9732 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@23999615
2025-05-27T14:39:51.464+02:00  INFO 9732 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-05-27T14:39:51.749+02:00  INFO 9732 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-27T14:39:52.641+02:00  INFO 9732 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-27T14:39:54.891+02:00  INFO 9732 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T14:39:54.923+02:00  WARN 9732 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-27T14:39:55.127+02:00  INFO 9732 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-05-27T14:39:56.850+02:00  WARN 9732 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-27T14:39:56.885+02:00  INFO 9732 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-27T14:39:57.118+02:00  INFO 9732 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-27T14:39:57.554+02:00  INFO 9732 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-27T14:39:57.587+02:00  INFO 9732 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-27T14:39:57.594+02:00  INFO 9732 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 9.135 seconds (process running for 9.762)
2025-05-27T14:40:01.610+02:00  INFO 9732 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-27T14:40:01.610+02:00  INFO 9732 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-27T14:40:01.611+02:00  INFO 9732 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-05-27T14:40:03.769+02:00  WARN 9732 --- [http-nio-8080-exec-1] c.h.evmodbus.services.ReportingService   : No serial ports found
2025-05-27T14:40:03.773+02:00  WARN 9732 --- [http-nio-8080-exec-1] c.h.evmodbus.services.ReportingService   : No serial ports found
2025-05-27T14:40:10.993+02:00  INFO 9732 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:40:10.993496900
2025-05-27T14:40:11.057+02:00  INFO 9732 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:40:10.993496900 using time unit HOUR
2025-05-27T14:40:11.072+02:00  INFO 9732 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T15:40:10.993496900
2025-05-27T14:40:11.136+02:00  INFO 9732 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Found 16 readings in extended date range
2025-05-27T14:40:11.136+02:00  INFO 9732 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Generated 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:40:11.136+02:00  INFO 9732 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:40:11.136+02:00  INFO 9732 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:40:11.136+02:00  INFO 9732 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0}
2025-05-27T14:40:11.136+02:00  INFO 9732 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:40:11.136388900
2025-05-27T14:40:11.199+02:00  INFO 9732 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:40:11.136388900 using time unit DAY
2025-05-27T14:40:11.199+02:00  INFO 9732 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:40:11.279+02:00  INFO 9732 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:40:11.279+02:00  INFO 9732 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:40:11.279+02:00  INFO 9732 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:40:11.279+02:00  INFO 9732 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:40:11.279+02:00  INFO 9732 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:40:11.279+02:00  INFO 9732 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:40:11.358+02:00  INFO 9732 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:40:11.358+02:00  INFO 9732 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:40:11.437+02:00  INFO 9732 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:40:11.437+02:00  INFO 9732 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:40:11.437+02:00  INFO 9732 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:40:11.437+02:00  INFO 9732 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:40:11.437+02:00  INFO 9732 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:40:11.437+02:00  INFO 9732 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:40:11.437+02:00  INFO 9732 --- [http-nio-8080-exec-3] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:40:18.148+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999
2025-05-27T14:40:19.360+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999 using time unit HOUR
2025-05-27T14:40:19.360+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-25T23:00 to 2025-05-27T00:59:59.999999999
2025-05-27T14:40:19.423+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Found 26 readings in extended date range
2025-05-27T14:40:19.423+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Generated 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T14:40:19.423+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T14:40:19.423+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-25T23:00:00.203725 = 1536.977, Last reading: 2025-05-27T00:00:00.200409 = 1546.675
2025-05-27T14:40:19.423+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.0, 01:00 - 02:00=0.0, 02:00 - 03:00=0.0, 03:00 - 04:00=0.0, 04:00 - 05:00=0.0, 05:00 - 06:00=0.0, 06:00 - 07:00=0.0, 07:00 - 08:00=0.0, 08:00 - 09:00=0.0, 09:00 - 10:00=0.0, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0, 15:00 - 16:00=0.0, 16:00 - 17:00=0.0, 17:00 - 18:00=0.0, 18:00 - 19:00=0.0, 19:00 - 20:00=0.3869629, 20:00 - 21:00=2.3790283, 21:00 - 22:00=2.3900146, 22:00 - 23:00=2.369995, 23:00 - 00:00=2.171997}
2025-05-27T14:40:19.550+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999
2025-05-27T14:40:19.629+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999 using time unit HOUR
2025-05-27T14:40:19.629+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-25T23:00 to 2025-05-27T00:59:59.999999999
2025-05-27T14:40:19.692+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Found 26 readings in extended date range
2025-05-27T14:40:19.692+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Generated 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T14:40:19.692+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T14:40:19.692+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-25T23:00:00.203725 = 1536.977, Last reading: 2025-05-27T00:00:00.200409 = 1546.675
2025-05-27T14:40:19.692+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.0, 01:00 - 02:00=0.0, 02:00 - 03:00=0.0, 03:00 - 04:00=0.0, 04:00 - 05:00=0.0, 05:00 - 06:00=0.0, 06:00 - 07:00=0.0, 07:00 - 08:00=0.0, 08:00 - 09:00=0.0, 09:00 - 10:00=0.0, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0, 15:00 - 16:00=0.0, 16:00 - 17:00=0.0, 17:00 - 18:00=0.0, 18:00 - 19:00=0.0, 19:00 - 20:00=0.3869629, 20:00 - 21:00=2.3790283, 21:00 - 22:00=2.3900146, 22:00 - 23:00=2.369995, 23:00 - 00:00=2.171997}
2025-05-27T14:40:19.692+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:40:19.692972200
2025-05-27T14:40:19.755+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:40:19.692972200 using time unit DAY
2025-05-27T14:40:19.755+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:40:19.829+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:40:19.829+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:40:19.829+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:40:19.829+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:40:19.829+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:40:19.829+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:40:19.899+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:40:19.899+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:40:19.994+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:40:19.994+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:40:19.994+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:40:19.994+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:40:19.994+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:40:19.994+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:40:19.994+02:00  INFO 9732 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:40:28.797+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:40:28.797386
2025-05-27T14:40:29.442+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:40:29.978+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:40:28.797386 using time unit DAY
2025-05-27T14:40:29.979+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:40:30.044+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:40:30.044+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:40:30.044+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:40:30.044+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:40:30.044+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:40:30.176+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999
2025-05-27T14:40:30.245+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999 using time unit HOUR
2025-05-27T14:40:30.245+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-25T23:00 to 2025-05-27T00:59:59.999999999
2025-05-27T14:40:30.299+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Found 26 readings in extended date range
2025-05-27T14:40:30.299+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Generated 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T14:40:30.299+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T14:40:30.299+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-25T23:00:00.203725 = 1536.977, Last reading: 2025-05-27T00:00:00.200409 = 1546.675
2025-05-27T14:40:30.299+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.0, 01:00 - 02:00=0.0, 02:00 - 03:00=0.0, 03:00 - 04:00=0.0, 04:00 - 05:00=0.0, 05:00 - 06:00=0.0, 06:00 - 07:00=0.0, 07:00 - 08:00=0.0, 08:00 - 09:00=0.0, 09:00 - 10:00=0.0, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0, 15:00 - 16:00=0.0, 16:00 - 17:00=0.0, 17:00 - 18:00=0.0, 18:00 - 19:00=0.0, 19:00 - 20:00=0.3869629, 20:00 - 21:00=2.3790283, 21:00 - 22:00=2.3900146, 22:00 - 23:00=2.369995, 23:00 - 00:00=2.171997}
2025-05-27T14:40:30.299+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:40:30.299155
2025-05-27T14:40:30.378+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:40:30.299155 using time unit DAY
2025-05-27T14:40:30.378+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:40:30.441+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:40:30.441+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:40:30.441+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:40:30.441+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:40:30.441+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:40:30.441+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:40:30.521+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:40:30.521+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:40:30.584+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:40:30.584+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:40:30.584+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:40:30.584+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:40:30.601+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:40:30.601+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:40:30.601+02:00  INFO 9732 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:40:30.683+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:40:30.683+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:40:30.758+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:40:30.758+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:40:30.758+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:40:30.758+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:40:30.768+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:40:30.768+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:40:30.768+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:40:30.908+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999
2025-05-27T14:40:30.973+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999 using time unit HOUR
2025-05-27T14:40:30.973+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-25T23:00 to 2025-05-27T00:59:59.999999999
2025-05-27T14:40:31.037+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Found 26 readings in extended date range
2025-05-27T14:40:31.037+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Generated 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T14:40:31.037+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T14:40:31.037+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-25T23:00:00.203725 = 1536.977, Last reading: 2025-05-27T00:00:00.200409 = 1546.675
2025-05-27T14:40:31.037+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.0, 01:00 - 02:00=0.0, 02:00 - 03:00=0.0, 03:00 - 04:00=0.0, 04:00 - 05:00=0.0, 05:00 - 06:00=0.0, 06:00 - 07:00=0.0, 07:00 - 08:00=0.0, 08:00 - 09:00=0.0, 09:00 - 10:00=0.0, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0, 15:00 - 16:00=0.0, 16:00 - 17:00=0.0, 17:00 - 18:00=0.0, 18:00 - 19:00=0.0, 19:00 - 20:00=0.3869629, 20:00 - 21:00=2.3790283, 21:00 - 22:00=2.3900146, 22:00 - 23:00=2.369995, 23:00 - 00:00=2.171997}
2025-05-27T14:40:31.037+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:40:31.037393100
2025-05-27T14:40:31.101+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:40:31.037393100 using time unit DAY
2025-05-27T14:40:31.101+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:40:31.164+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:40:31.164+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:40:31.164+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:40:31.164+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:40:31.164+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:40:31.164+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:40:31.243+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:40:31.243+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:40:31.322+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:40:31.322+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:40:31.322+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:40:31.322+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:40:31.322+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:40:31.322+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:40:31.322+02:00  INFO 9732 --- [http-nio-8080-exec-6] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:40:32.661+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999
2025-05-27T14:40:33.866+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999 using time unit HOUR
2025-05-27T14:40:33.866+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-25T23:00 to 2025-05-27T00:59:59.999999999
2025-05-27T14:40:33.941+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Found 26 readings in extended date range
2025-05-27T14:40:33.941+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Generated 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T14:40:33.941+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T14:40:33.941+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-25T23:00:00.203725 = 1536.977, Last reading: 2025-05-27T00:00:00.200409 = 1546.675
2025-05-27T14:40:33.942+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.0, 01:00 - 02:00=0.0, 02:00 - 03:00=0.0, 03:00 - 04:00=0.0, 04:00 - 05:00=0.0, 05:00 - 06:00=0.0, 06:00 - 07:00=0.0, 07:00 - 08:00=0.0, 08:00 - 09:00=0.0, 09:00 - 10:00=0.0, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0, 15:00 - 16:00=0.0, 16:00 - 17:00=0.0, 17:00 - 18:00=0.0, 18:00 - 19:00=0.0, 19:00 - 20:00=0.3869629, 20:00 - 21:00=2.3790283, 21:00 - 22:00=2.3900146, 22:00 - 23:00=2.369995, 23:00 - 00:00=2.171997}
2025-05-27T14:40:34.067+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999
2025-05-27T14:40:34.136+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-26T00:00 to 2025-05-26T23:59:59.999999999 using time unit HOUR
2025-05-27T14:40:34.136+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-25T23:00 to 2025-05-27T00:59:59.999999999
2025-05-27T14:40:34.203+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Found 26 readings in extended date range
2025-05-27T14:40:34.203+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Generated 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T14:40:34.203+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-27T14:40:34.203+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-25T23:00:00.203725 = 1536.977, Last reading: 2025-05-27T00:00:00.200409 = 1546.675
2025-05-27T14:40:34.203+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.0, 01:00 - 02:00=0.0, 02:00 - 03:00=0.0, 03:00 - 04:00=0.0, 04:00 - 05:00=0.0, 05:00 - 06:00=0.0, 06:00 - 07:00=0.0, 07:00 - 08:00=0.0, 08:00 - 09:00=0.0, 09:00 - 10:00=0.0, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0, 15:00 - 16:00=0.0, 16:00 - 17:00=0.0, 17:00 - 18:00=0.0, 18:00 - 19:00=0.0, 19:00 - 20:00=0.3869629, 20:00 - 21:00=2.3790283, 21:00 - 22:00=2.3900146, 22:00 - 23:00=2.369995, 23:00 - 00:00=2.171997}
2025-05-27T14:40:34.203+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:40:34.203566
2025-05-27T14:40:34.269+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:40:34.203566 using time unit DAY
2025-05-27T14:40:34.269+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:40:34.337+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:40:34.337+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:40:34.337+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:40:34.338+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:40:34.338+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:40:34.338+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:40:34.413+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:40:34.413+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:40:34.480+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:40:34.480+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:40:34.480+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:40:34.480+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:40:34.498+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:40:34.498+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:40:34.498+02:00  INFO 9732 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:40:38.910+02:00  WARN 9732 --- [http-nio-8080-exec-8] c.h.evmodbus.services.ReportingService   : No serial ports found
2025-05-27T14:40:38.910+02:00  WARN 9732 --- [http-nio-8080-exec-8] c.h.evmodbus.services.ReportingService   : No serial ports found
2025-05-27T14:40:43.092+02:00  INFO 9732 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:40:43.092995900
2025-05-27T14:40:43.156+02:00  INFO 9732 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:40:43.092995900 using time unit HOUR
2025-05-27T14:40:43.156+02:00  INFO 9732 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T15:40:43.092995900
2025-05-27T14:40:43.226+02:00  INFO 9732 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Found 16 readings in extended date range
2025-05-27T14:40:43.226+02:00  INFO 9732 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Generated 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:40:43.226+02:00  INFO 9732 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:40:43.226+02:00  INFO 9732 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:40:43.228+02:00  INFO 9732 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0}
2025-05-27T14:40:43.228+02:00  INFO 9732 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:40:43.228058800
2025-05-27T14:40:43.294+02:00  INFO 9732 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:40:43.228058800 using time unit DAY
2025-05-27T14:40:43.294+02:00  INFO 9732 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:40:43.355+02:00  INFO 9732 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:40:43.355+02:00  INFO 9732 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:40:43.355+02:00  INFO 9732 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:40:43.355+02:00  INFO 9732 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:40:43.355+02:00  INFO 9732 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:40:43.355+02:00  INFO 9732 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:40:43.435+02:00  INFO 9732 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:40:43.435+02:00  INFO 9732 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:40:43.510+02:00  INFO 9732 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:40:43.510+02:00  INFO 9732 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:40:43.510+02:00  INFO 9732 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:40:43.511+02:00  INFO 9732 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:40:43.514+02:00  INFO 9732 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:40:43.514+02:00  INFO 9732 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:40:43.514+02:00  INFO 9732 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:40:45.559+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:40:45.559280200
2025-05-27T14:40:46.635+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:40:45.559280200 using time unit DAY
2025-05-27T14:40:46.635+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:40:46.699+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:40:46.699+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:40:46.699+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:40:46.699+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:40:46.699+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:40:46.841+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:40:46.841082100
2025-05-27T14:40:46.904+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:40:46.841082100 using time unit HOUR
2025-05-27T14:40:46.904+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T15:40:46.841082100
2025-05-27T14:40:46.968+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 16 readings in extended date range
2025-05-27T14:40:46.968+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:40:46.968+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:40:46.968+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:40:46.968+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0}
2025-05-27T14:40:46.968+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:40:46.968461300
2025-05-27T14:40:47.032+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:40:46.968461300 using time unit DAY
2025-05-27T14:40:47.032+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:40:47.095+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:40:47.095+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:40:47.095+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:40:47.095+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:40:47.111+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:40:47.111+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:40:47.174+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:40:47.174+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:40:47.254+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:40:47.254+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:40:47.254+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:40:47.254+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:40:47.254+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:40:47.254+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:40:47.254+02:00  INFO 9732 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:40:48.995+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:40:48.995507500
2025-05-27T14:40:50.117+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:40:48.995507500 using time unit HOUR
2025-05-27T14:40:50.117+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T15:40:48.995507500
2025-05-27T14:40:50.180+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Found 16 readings in extended date range
2025-05-27T14:40:50.180+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Generated 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:40:50.180+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:40:50.180+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:40:50.180+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0}
2025-05-27T14:40:50.325+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:40:50.325107100
2025-05-27T14:40:50.390+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T14:40:50.325107100 using time unit HOUR
2025-05-27T14:40:50.390+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T15:40:50.325107100
2025-05-27T14:40:50.456+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Found 16 readings in extended date range
2025-05-27T14:40:50.456+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Generated 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:40:50.456+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 15 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00]
2025-05-27T14:40:50.456+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:40:50.456+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0}
2025-05-27T14:40:50.456+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:40:50.456178400
2025-05-27T14:40:50.524+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T14:40:50.456178400 using time unit DAY
2025-05-27T14:40:50.524+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T14:40:50.589+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Found 663 readings in extended date range
2025-05-27T14:40:50.589+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:40:50.589+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T14:40:50.589+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:40:50.591+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T14:40:50.591+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T14:40:50.664+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T14:40:50.665+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T14:40:50.730+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Found 4229 readings in extended date range
2025-05-27T14:40:50.730+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:40:50.730+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T14:40:50.730+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T14:00:00.222562 = 1546.947
2025-05-27T14:40:50.730+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T14:40:50.730+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T14:40:50.730+02:00  INFO 9732 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T15:03:01.743+02:00  INFO 9732 --- [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 48 class path changes (0 additions, 0 deletions, 48 modifications)
2025-05-27T15:03:01.758+02:00  INFO 9732 --- [Thread-6] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-27T15:03:01.770+02:00  INFO 9732 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-27T15:03:01.782+02:00  INFO 9732 --- [Thread-6] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T15:03:01.786+02:00  INFO 9732 --- [Thread-6] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-05-27T15:03:02.144+02:00  INFO 9732 --- [Thread-6] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-05-27T15:03:02.246+02:00  INFO 9732 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 9732 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-27T15:03:02.246+02:00  INFO 9732 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-27T15:03:02.558+02:00  INFO 9732 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-27T15:03:02.592+02:00  INFO 9732 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 33 ms. Found 10 JPA repository interfaces.
2025-05-27T15:03:02.744+02:00  INFO 9732 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-27T15:03:02.745+02:00  INFO 9732 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-27T15:03:02.745+02:00  INFO 9732 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-27T15:03:02.769+02:00  INFO 9732 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-27T15:03:02.769+02:00  INFO 9732 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 518 ms
2025-05-27T15:03:02.855+02:00  INFO 9732 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-27T15:03:02.859+02:00  INFO 9732 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-27T15:03:02.868+02:00  INFO 9732 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-27T15:03:02.869+02:00  INFO 9732 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-05-27T15:03:03.475+02:00  INFO 9732 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@79a8ea1a
2025-05-27T15:03:03.475+02:00  INFO 9732 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-05-27T15:03:03.713+02:00  INFO 9732 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-2)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-27T15:03:03.898+02:00  INFO 9732 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-27T15:03:06.045+02:00  INFO 9732 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T15:03:06.066+02:00  WARN 9732 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-27T15:03:06.455+02:00  WARN 9732 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-27T15:03:06.480+02:00  INFO 9732 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-27T15:03:06.610+02:00  INFO 9732 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-27T15:03:06.840+02:00  INFO 9732 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-27T15:03:06.854+02:00  INFO 9732 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-27T15:03:06.857+02:00  INFO 9732 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 4.658 seconds (process running for 1399.025)
2025-05-27T15:03:07.184+02:00  INFO 9732 --- [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-05-27T15:04:57.483+02:00  INFO 9732 --- [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-27T15:04:57.483+02:00  INFO 9732 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-27T15:04:57.483+02:00  INFO 9732 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T15:04:57.483+02:00  INFO 9732 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-05-27T15:04:57.783+02:00  INFO 9732 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-05-27T15:04:59.860+02:00  INFO 19336 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 19336 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-27T15:04:59.860+02:00  INFO 19336 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-27T15:04:59.900+02:00  INFO 19336 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-05-27T15:04:59.902+02:00  INFO 19336 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-05-27T15:05:00.528+02:00  INFO 19336 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-27T15:05:00.595+02:00  INFO 19336 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 68 ms. Found 10 JPA repository interfaces.
2025-05-27T15:05:01.093+02:00  INFO 19336 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-27T15:05:01.099+02:00  INFO 19336 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-27T15:05:01.099+02:00  INFO 19336 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-27T15:05:01.131+02:00  INFO 19336 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-27T15:05:01.131+02:00  INFO 19336 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1229 ms
2025-05-27T15:05:01.305+02:00  INFO 19336 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-27T15:05:01.354+02:00  INFO 19336 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.15.Final
2025-05-27T15:05:01.384+02:00  INFO 19336 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-27T15:05:01.617+02:00  INFO 19336 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-27T15:05:01.645+02:00  INFO 19336 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-05-27T15:05:02.458+02:00  INFO 19336 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@5fab0a71
2025-05-27T15:05:02.459+02:00  INFO 19336 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-05-27T15:05:02.743+02:00  INFO 19336 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-27T15:05:03.672+02:00  INFO 19336 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-27T15:05:05.918+02:00  INFO 19336 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T15:05:05.948+02:00  WARN 19336 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-27T15:05:06.151+02:00  INFO 19336 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-05-27T15:05:07.813+02:00  WARN 19336 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-27T15:05:07.845+02:00  INFO 19336 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-27T15:05:08.052+02:00  INFO 19336 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-27T15:05:08.493+02:00  INFO 19336 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-27T15:05:08.525+02:00  INFO 19336 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-27T15:05:08.531+02:00  INFO 19336 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 9.048 seconds (process running for 9.882)
2025-05-27T15:05:13.057+02:00  INFO 19336 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-27T15:05:13.057+02:00  INFO 19336 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-27T15:05:13.058+02:00  INFO 19336 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-05-27T15:08:21.637+02:00  INFO 19336 --- [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-27T15:08:21.641+02:00  INFO 19336 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-27T15:08:21.646+02:00  INFO 19336 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T15:08:21.648+02:00  INFO 19336 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-05-27T15:08:21.955+02:00  INFO 19336 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-05-27T15:08:24.992+02:00  INFO 17032 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 17032 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-27T15:08:24.994+02:00  INFO 17032 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-27T15:08:25.032+02:00  INFO 17032 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-05-27T15:08:25.033+02:00  INFO 17032 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-05-27T15:08:25.647+02:00  INFO 17032 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-27T15:08:25.722+02:00  INFO 17032 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 68 ms. Found 10 JPA repository interfaces.
2025-05-27T15:08:26.229+02:00  INFO 17032 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-27T15:08:26.240+02:00  INFO 17032 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-27T15:08:26.240+02:00  INFO 17032 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-27T15:08:26.277+02:00  INFO 17032 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-27T15:08:26.277+02:00  INFO 17032 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1244 ms
2025-05-27T15:08:26.457+02:00  INFO 17032 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-27T15:08:26.510+02:00  INFO 17032 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.15.Final
2025-05-27T15:08:26.537+02:00  INFO 17032 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-27T15:08:26.770+02:00  INFO 17032 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-27T15:08:26.796+02:00  INFO 17032 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-05-27T15:08:27.609+02:00  INFO 17032 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@2ba43287
2025-05-27T15:08:27.610+02:00  INFO 17032 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-05-27T15:08:27.894+02:00  INFO 17032 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-27T15:08:28.765+02:00  INFO 17032 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-27T15:08:31.013+02:00  INFO 17032 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T15:08:31.056+02:00  WARN 17032 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-27T15:08:31.312+02:00  INFO 17032 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-05-27T15:08:33.039+02:00  WARN 17032 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-27T15:08:33.071+02:00  INFO 17032 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-27T15:08:33.271+02:00  INFO 17032 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-27T15:08:33.702+02:00  INFO 17032 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-27T15:08:33.736+02:00  INFO 17032 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-27T15:08:33.743+02:00  INFO 17032 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 9.143 seconds (process running for 9.988)
2025-05-27T15:08:33.781+02:00  INFO 17032 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-27T15:08:33.781+02:00  INFO 17032 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-27T15:08:33.782+02:00  INFO 17032 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-05-27T15:08:36.004+02:00  WARN 17032 --- [http-nio-8080-exec-1] c.h.evmodbus.services.ReportingService   : No serial ports found
2025-05-27T15:08:36.007+02:00  WARN 17032 --- [http-nio-8080-exec-1] c.h.evmodbus.services.ReportingService   : No serial ports found
2025-05-27T15:13:04.359+02:00  INFO 17032 --- [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 49 class path changes (1 addition, 0 deletions, 48 modifications)
2025-05-27T15:13:04.363+02:00  INFO 17032 --- [Thread-6] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-27T15:13:04.367+02:00  INFO 17032 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-27T15:13:04.372+02:00  INFO 17032 --- [Thread-6] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T15:13:04.374+02:00  INFO 17032 --- [Thread-6] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-05-27T15:13:04.667+02:00  INFO 17032 --- [Thread-6] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-05-27T15:13:04.740+02:00  INFO 17032 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 17032 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-27T15:13:04.740+02:00  INFO 17032 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-27T15:13:04.984+02:00  INFO 17032 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-27T15:13:05.017+02:00  INFO 17032 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 32 ms. Found 10 JPA repository interfaces.
2025-05-27T15:13:05.138+02:00  INFO 17032 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-27T15:13:05.139+02:00  INFO 17032 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-27T15:13:05.139+02:00  INFO 17032 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-27T15:13:05.157+02:00  INFO 17032 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-27T15:13:05.157+02:00  INFO 17032 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 415 ms
2025-05-27T15:13:05.240+02:00  INFO 17032 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-27T15:13:05.243+02:00  INFO 17032 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-27T15:13:05.250+02:00  INFO 17032 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-27T15:13:05.251+02:00  INFO 17032 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-05-27T15:13:05.862+02:00  INFO 17032 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@62c959e0
2025-05-27T15:13:05.862+02:00  INFO 17032 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-05-27T15:13:06.101+02:00  INFO 17032 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-2)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-27T15:13:06.274+02:00  INFO 17032 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-27T15:13:08.430+02:00  INFO 17032 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T15:13:08.446+02:00  WARN 17032 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-27T15:13:08.845+02:00  WARN 17032 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-27T15:13:08.876+02:00  INFO 17032 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-27T15:13:09.002+02:00  INFO 17032 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-27T15:13:09.258+02:00  INFO 17032 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-27T15:13:09.270+02:00  INFO 17032 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-27T15:13:09.272+02:00  INFO 17032 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 4.558 seconds (process running for 285.517)
2025-05-27T15:13:09.599+02:00  INFO 17032 --- [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-05-27T15:15:59.054+02:00  INFO 17032 --- [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-27T15:15:59.058+02:00  INFO 17032 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-27T15:15:59.060+02:00  INFO 17032 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T15:15:59.061+02:00  INFO 17032 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-05-27T15:15:59.410+02:00  INFO 17032 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-05-27T15:16:02.520+02:00  INFO 12760 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 12760 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-27T15:16:02.522+02:00  INFO 12760 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-27T15:16:02.559+02:00  INFO 12760 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-05-27T15:16:02.559+02:00  INFO 12760 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-05-27T15:16:03.167+02:00  INFO 12760 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-27T15:16:03.246+02:00  INFO 12760 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 73 ms. Found 10 JPA repository interfaces.
2025-05-27T15:16:03.748+02:00  INFO 12760 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-27T15:16:03.759+02:00  INFO 12760 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-27T15:16:03.759+02:00  INFO 12760 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-27T15:16:03.796+02:00  INFO 12760 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-27T15:16:03.796+02:00  INFO 12760 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1236 ms
2025-05-27T15:16:03.969+02:00  INFO 12760 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-27T15:16:04.015+02:00  INFO 12760 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.15.Final
2025-05-27T15:16:04.041+02:00  INFO 12760 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-27T15:16:04.272+02:00  INFO 12760 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-27T15:16:04.297+02:00  INFO 12760 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-05-27T15:16:05.104+02:00  INFO 12760 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@4d8352af
2025-05-27T15:16:05.105+02:00  INFO 12760 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-05-27T15:16:05.394+02:00  INFO 12760 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-27T15:16:06.245+02:00  INFO 12760 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-27T15:16:08.491+02:00  INFO 12760 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T15:16:08.525+02:00  WARN 12760 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-27T15:16:08.735+02:00  INFO 12760 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-05-27T15:16:10.368+02:00  WARN 12760 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-27T15:16:10.398+02:00  INFO 12760 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-27T15:16:10.601+02:00  INFO 12760 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-27T15:16:11.019+02:00  INFO 12760 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-27T15:16:11.051+02:00  INFO 12760 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-27T15:16:11.058+02:00  INFO 12760 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 8.944 seconds (process running for 10.358)
2025-05-27T15:16:19.608+02:00  INFO 12760 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-27T15:16:19.609+02:00  INFO 12760 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-27T15:16:19.610+02:00  INFO 12760 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-05-27T15:16:23.137+02:00  INFO 12760 --- [Thread-8] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:16:23 CEST 2025
2025-05-27T15:16:23.137+02:00 ERROR 12760 --- [Thread-8] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@6fadcfcc: Master is null.
2025-05-27T15:16:23.524+02:00  INFO 12760 --- [Thread-9] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:16:23 CEST 2025
2025-05-27T15:16:23.525+02:00 ERROR 12760 --- [Thread-9] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@5052fde3: Master is null.
2025-05-27T15:16:23.581+02:00  INFO 12760 --- [Thread-9] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:16:23 CEST 2025
2025-05-27T15:16:23.581+02:00 ERROR 12760 --- [Thread-9] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@12a13bce: Master is null.
2025-05-27T15:16:23.643+02:00  INFO 12760 --- [Thread-9] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:16:23 CEST 2025
2025-05-27T15:16:23.643+02:00 ERROR 12760 --- [Thread-9] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@22fc324: Master is null.
2025-05-27T15:16:23.704+02:00  INFO 12760 --- [Thread-9] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:16:23 CEST 2025
2025-05-27T15:16:23.704+02:00 ERROR 12760 --- [Thread-9] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@18f7ac6b: Master is null.
2025-05-27T15:16:24.138+02:00  INFO 12760 --- [Thread-10] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:16:24 CEST 2025
2025-05-27T15:16:24.138+02:00 ERROR 12760 --- [Thread-10] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@7dabb916: Master is null.
2025-05-27T15:16:24.402+02:00  INFO 12760 --- [Thread-11] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:16:24 CEST 2025
2025-05-27T15:16:24.402+02:00 ERROR 12760 --- [Thread-11] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@370465e2: Master is null.
2025-05-27T15:16:24.467+02:00  INFO 12760 --- [Thread-11] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:16:24 CEST 2025
2025-05-27T15:16:24.468+02:00 ERROR 12760 --- [Thread-11] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@4705571a: Master is null.
2025-05-27T15:16:24.522+02:00  INFO 12760 --- [Thread-11] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:16:24 CEST 2025
2025-05-27T15:16:24.523+02:00 ERROR 12760 --- [Thread-11] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@18463f5: Master is null.
2025-05-27T15:16:24.657+02:00  INFO 12760 --- [Thread-12] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:16:24 CEST 2025
2025-05-27T15:16:24.658+02:00 ERROR 12760 --- [Thread-12] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@51a71863: Master is null.
2025-05-27T15:16:24.995+02:00  INFO 12760 --- [Thread-13] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:16:24 CEST 2025
2025-05-27T15:16:24.995+02:00 ERROR 12760 --- [Thread-13] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@166e669c: Master is null.
2025-05-27T15:16:25.306+02:00  INFO 12760 --- [Thread-14] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:16:25 CEST 2025
2025-05-27T15:16:25.306+02:00 ERROR 12760 --- [Thread-14] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@202ce0e7: Master is null.
2025-05-27T15:16:25.619+02:00  INFO 12760 --- [Thread-15] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 15:16:25 CEST 2025
2025-05-27T15:16:25.619+02:00 ERROR 12760 --- [Thread-15] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@38b469fa: Master is null.
2025-05-27T15:16:25.671+02:00  INFO 12760 --- [Thread-15] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 15:16:25 CEST 2025
2025-05-27T15:16:25.671+02:00 ERROR 12760 --- [Thread-15] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@2c10faa3: Master is null.
2025-05-27T15:16:25.734+02:00  INFO 12760 --- [Thread-15] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 15:16:25 CEST 2025
2025-05-27T15:16:25.734+02:00 ERROR 12760 --- [Thread-15] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@580f4a20: Master is null.
2025-05-27T15:16:26.063+02:00  INFO 12760 --- [Thread-16] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 15:16:26 CEST 2025
2025-05-27T15:16:26.063+02:00 ERROR 12760 --- [Thread-16] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@1a183294: Master is null.
2025-05-27T15:16:26.385+02:00  INFO 12760 --- [Thread-17] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 15:16:26 CEST 2025
2025-05-27T15:16:26.385+02:00 ERROR 12760 --- [Thread-17] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@40c2def7: Master is null.
2025-05-27T15:16:26.697+02:00  INFO 12760 --- [Thread-18] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 15:16:26 CEST 2025
2025-05-27T15:16:26.697+02:00 ERROR 12760 --- [Thread-18] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@1d7026c9: Master is null.
2025-05-27T15:16:33.356+02:00  INFO 12760 --- [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 49 class path changes (0 additions, 0 deletions, 49 modifications)
2025-05-27T15:16:33.356+02:00  INFO 12760 --- [Thread-6] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-27T15:16:33.356+02:00  INFO 12760 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-27T15:16:33.372+02:00  INFO 12760 --- [Thread-6] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T15:16:33.372+02:00  INFO 12760 --- [Thread-6] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-05-27T15:16:33.672+02:00  INFO 12760 --- [Thread-6] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-05-27T15:16:33.768+02:00  INFO 12760 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 12760 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-27T15:16:33.769+02:00  INFO 12760 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-27T15:16:34.227+02:00  INFO 12760 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-27T15:16:34.276+02:00  INFO 12760 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 50 ms. Found 10 JPA repository interfaces.
2025-05-27T15:16:34.455+02:00  INFO 12760 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-27T15:16:34.455+02:00  INFO 12760 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-27T15:16:34.455+02:00  INFO 12760 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-27T15:16:34.488+02:00  INFO 12760 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-27T15:16:34.488+02:00  INFO 12760 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 717 ms
2025-05-27T15:16:34.586+02:00  INFO 12760 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-27T15:16:34.591+02:00  INFO 12760 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-27T15:16:34.600+02:00  INFO 12760 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-27T15:16:34.600+02:00  INFO 12760 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-05-27T15:16:35.215+02:00  INFO 12760 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@2e0beb16
2025-05-27T15:16:35.215+02:00  INFO 12760 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-05-27T15:16:35.453+02:00  INFO 12760 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-2)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-27T15:16:35.665+02:00  INFO 12760 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-27T15:16:37.815+02:00  INFO 12760 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T15:16:37.838+02:00  WARN 12760 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-27T15:16:38.189+02:00  WARN 12760 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-27T15:16:38.209+02:00  INFO 12760 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-27T15:16:38.330+02:00  INFO 12760 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-27T15:16:38.632+02:00  INFO 12760 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-27T15:16:38.655+02:00  INFO 12760 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-27T15:16:38.657+02:00  INFO 12760 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 4.938 seconds (process running for 37.957)
2025-05-27T15:16:38.981+02:00  INFO 12760 --- [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-05-27T15:17:09.999+02:00  INFO 12760 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-27T15:17:10.000+02:00  INFO 12760 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-27T15:17:10.000+02:00  INFO 12760 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 0 ms
2025-05-27T15:17:13.001+02:00  INFO 12760 --- [Thread-23] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:17:13 CEST 2025
2025-05-27T15:17:13.002+02:00 ERROR 12760 --- [Thread-23] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@69c449d6: Master is null.
2025-05-27T15:17:13.369+02:00  INFO 12760 --- [Thread-24] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:17:13 CEST 2025
2025-05-27T15:17:13.369+02:00 ERROR 12760 --- [Thread-24] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@6cb4a5bc: Master is null.
2025-05-27T15:17:13.432+02:00  INFO 12760 --- [Thread-24] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:17:13 CEST 2025
2025-05-27T15:17:13.432+02:00 ERROR 12760 --- [Thread-24] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@7576cd4f: Master is null.
2025-05-27T15:17:13.493+02:00  INFO 12760 --- [Thread-24] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:17:13 CEST 2025
2025-05-27T15:17:13.493+02:00 ERROR 12760 --- [Thread-24] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@4397da9f: Master is null.
2025-05-27T15:17:13.556+02:00  INFO 12760 --- [Thread-24] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:17:13 CEST 2025
2025-05-27T15:17:13.556+02:00 ERROR 12760 --- [Thread-24] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@578bd438: Master is null.
2025-05-27T15:17:13.812+02:00  INFO 12760 --- [Thread-25] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:17:13 CEST 2025
2025-05-27T15:17:13.812+02:00 ERROR 12760 --- [Thread-25] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@b748179: Master is null.
2025-05-27T15:17:14.154+02:00  INFO 12760 --- [Thread-26] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:17:14 CEST 2025
2025-05-27T15:17:14.154+02:00 ERROR 12760 --- [Thread-26] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@509664ed: Master is null.
2025-05-27T15:17:14.212+02:00  INFO 12760 --- [Thread-26] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:17:14 CEST 2025
2025-05-27T15:17:14.212+02:00 ERROR 12760 --- [Thread-26] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@3e2a990: Master is null.
2025-05-27T15:17:14.275+02:00  INFO 12760 --- [Thread-26] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:17:14 CEST 2025
2025-05-27T15:17:14.275+02:00 ERROR 12760 --- [Thread-26] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@4fc879eb: Master is null.
2025-05-27T15:17:14.338+02:00  INFO 12760 --- [Thread-26] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:17:14 CEST 2025
2025-05-27T15:17:14.338+02:00 ERROR 12760 --- [Thread-26] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@1e068608: Master is null.
2025-05-27T15:17:14.613+02:00  INFO 12760 --- [Thread-27] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:17:14 CEST 2025
2025-05-27T15:17:14.614+02:00 ERROR 12760 --- [Thread-27] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@e9d253b: Master is null.
2025-05-27T15:17:15.004+02:00  INFO 12760 --- [Thread-28] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:17:15 CEST 2025
2025-05-27T15:17:15.004+02:00 ERROR 12760 --- [Thread-28] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@3d78c38c: Master is null.
2025-05-27T15:17:15.323+02:00  INFO 12760 --- [Thread-29] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 15:17:15 CEST 2025
2025-05-27T15:17:15.323+02:00 ERROR 12760 --- [Thread-29] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@2469603c: Master is null.
2025-05-27T15:17:15.404+02:00  INFO 12760 --- [Thread-30] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 15:17:15 CEST 2025
2025-05-27T15:17:15.404+02:00 ERROR 12760 --- [Thread-30] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@4f4b35d9: Master is null.
2025-05-27T15:17:15.475+02:00  INFO 12760 --- [Thread-31] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 15:17:15 CEST 2025
2025-05-27T15:17:15.475+02:00 ERROR 12760 --- [Thread-31] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@510ca67: Master is null.
2025-05-27T15:17:15.716+02:00  INFO 12760 --- [Thread-32] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 15:17:15 CEST 2025
2025-05-27T15:17:15.716+02:00 ERROR 12760 --- [Thread-32] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@772c652e: Master is null.
2025-05-27T15:17:16.110+02:00  INFO 12760 --- [Thread-33] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 15:17:16 CEST 2025
2025-05-27T15:17:16.110+02:00 ERROR 12760 --- [Thread-33] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@5bb94850: Master is null.
2025-05-27T15:17:16.514+02:00  INFO 12760 --- [Thread-34] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 15:17:16 CEST 2025
2025-05-27T15:17:16.514+02:00 ERROR 12760 --- [Thread-34] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@5bd658: Master is null.
2025-05-27T15:17:28.195+02:00  INFO 12760 --- [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-27T15:17:28.199+02:00  INFO 12760 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-27T15:17:28.202+02:00  INFO 12760 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T15:17:28.203+02:00  INFO 12760 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-05-27T15:17:28.496+02:00  INFO 12760 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-05-27T15:20:22.174+02:00  INFO 14720 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 14720 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-27T15:20:22.176+02:00  INFO 14720 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-27T15:20:22.217+02:00  INFO 14720 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-05-27T15:20:22.218+02:00  INFO 14720 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-05-27T15:20:22.875+02:00  INFO 14720 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-27T15:20:22.963+02:00  INFO 14720 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 77 ms. Found 10 JPA repository interfaces.
2025-05-27T15:20:23.494+02:00  INFO 14720 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-27T15:20:23.505+02:00  INFO 14720 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-27T15:20:23.505+02:00  INFO 14720 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-27T15:20:23.543+02:00  INFO 14720 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-27T15:20:23.543+02:00  INFO 14720 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1325 ms
2025-05-27T15:20:23.728+02:00  INFO 14720 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-27T15:20:23.777+02:00  INFO 14720 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.15.Final
2025-05-27T15:20:23.805+02:00  INFO 14720 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-27T15:20:24.041+02:00  INFO 14720 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-27T15:20:24.067+02:00  INFO 14720 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-05-27T15:20:24.882+02:00  INFO 14720 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@1c740eff
2025-05-27T15:20:24.883+02:00  INFO 14720 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-05-27T15:20:25.168+02:00  INFO 14720 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-27T15:20:26.036+02:00  INFO 14720 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-27T15:20:28.285+02:00  INFO 14720 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T15:20:28.315+02:00  WARN 14720 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-27T15:20:28.520+02:00  INFO 14720 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-05-27T15:20:30.144+02:00  WARN 14720 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-27T15:20:30.174+02:00  INFO 14720 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-27T15:20:30.372+02:00  INFO 14720 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-27T15:20:30.798+02:00  INFO 14720 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-27T15:20:30.830+02:00  INFO 14720 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-27T15:20:30.837+02:00  INFO 14720 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 9.055 seconds (process running for 10.094)
2025-05-27T15:20:48.889+02:00  INFO 14720 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-27T15:20:48.889+02:00  INFO 14720 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-27T15:20:48.890+02:00  INFO 14720 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-05-27T15:20:52.343+02:00  INFO 14720 --- [Thread-8] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:20:52 CEST 2025
2025-05-27T15:20:52.343+02:00 ERROR 14720 --- [Thread-8] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@2b12955: Master is null.
2025-05-27T15:20:52.752+02:00  INFO 14720 --- [Thread-9] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:20:52 CEST 2025
2025-05-27T15:20:52.752+02:00 ERROR 14720 --- [Thread-9] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@1a2028ea: Master is null.
2025-05-27T15:20:52.813+02:00  INFO 14720 --- [Thread-9] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:20:52 CEST 2025
2025-05-27T15:20:52.813+02:00 ERROR 14720 --- [Thread-9] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@4433956a: Master is null.
2025-05-27T15:20:52.869+02:00  INFO 14720 --- [Thread-9] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:20:52 CEST 2025
2025-05-27T15:20:52.869+02:00 ERROR 14720 --- [Thread-9] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@192cfbc3: Master is null.
2025-05-27T15:20:52.921+02:00  INFO 14720 --- [Thread-9] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:20:52 CEST 2025
2025-05-27T15:20:52.921+02:00 ERROR 14720 --- [Thread-9] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@278a1bd8: Master is null.
2025-05-27T15:20:53.189+02:00  INFO 14720 --- [Thread-10] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:20:53 CEST 2025
2025-05-27T15:20:53.189+02:00 ERROR 14720 --- [Thread-10] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@18346558: Master is null.
2025-05-27T15:20:53.572+02:00  INFO 14720 --- [Thread-11] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:20:53 CEST 2025
2025-05-27T15:20:53.572+02:00 ERROR 14720 --- [Thread-11] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@2724ceb4: Master is null.
2025-05-27T15:20:53.633+02:00  INFO 14720 --- [Thread-11] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:20:53 CEST 2025
2025-05-27T15:20:53.633+02:00 ERROR 14720 --- [Thread-11] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@58c82392: Master is null.
2025-05-27T15:20:53.690+02:00  INFO 14720 --- [Thread-11] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:20:53 CEST 2025
2025-05-27T15:20:53.690+02:00 ERROR 14720 --- [Thread-11] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@1e5980f5: Master is null.
2025-05-27T15:20:53.745+02:00  INFO 14720 --- [Thread-11] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:20:53 CEST 2025
2025-05-27T15:20:53.745+02:00 ERROR 14720 --- [Thread-11] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@592e21c7: Master is null.
2025-05-27T15:20:53.989+02:00  INFO 14720 --- [Thread-12] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:20:53 CEST 2025
2025-05-27T15:20:53.989+02:00 ERROR 14720 --- [Thread-12] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@796c8b9d: Master is null.
2025-05-27T15:20:54.410+02:00  INFO 14720 --- [Thread-13] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Tue May 27 15:20:54 CEST 2025
2025-05-27T15:20:54.410+02:00 ERROR 14720 --- [Thread-13] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@5f8f68ce: Master is null.
2025-05-27T15:20:54.709+02:00  INFO 14720 --- [Thread-14] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 15:20:54 CEST 2025
2025-05-27T15:20:54.709+02:00 ERROR 14720 --- [Thread-14] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@4849153a: Master is null.
2025-05-27T15:20:54.774+02:00  INFO 14720 --- [Thread-14] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 15:20:54 CEST 2025
2025-05-27T15:20:54.774+02:00 ERROR 14720 --- [Thread-14] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@4c47cfd6: Master is null.
2025-05-27T15:20:54.867+02:00  INFO 14720 --- [Thread-15] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 15:20:54 CEST 2025
2025-05-27T15:20:54.869+02:00 ERROR 14720 --- [Thread-15] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@77cecd04: Master is null.
2025-05-27T15:20:55.157+02:00  INFO 14720 --- [Thread-16] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 15:20:55 CEST 2025
2025-05-27T15:20:55.157+02:00 ERROR 14720 --- [Thread-16] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@4b9516d4: Master is null.
2025-05-27T15:20:55.509+02:00  INFO 14720 --- [Thread-17] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 15:20:55 CEST 2025
2025-05-27T15:20:55.509+02:00 ERROR 14720 --- [Thread-17] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@6e22ae6a: Master is null.
2025-05-27T15:20:55.783+02:00  INFO 14720 --- [Thread-18] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Tue May 27 15:20:55 CEST 2025
2025-05-27T15:20:55.783+02:00 ERROR 14720 --- [Thread-18] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@62f29040: Master is null.
2025-05-27T15:20:58.254+02:00  INFO 14720 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T15:20:58.254938700
2025-05-27T15:20:58.320+02:00  INFO 14720 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T15:20:58.254938700 using time unit HOUR
2025-05-27T15:20:58.320+02:00  INFO 14720 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-27T16:20:58.254938700
2025-05-27T15:20:58.382+02:00  INFO 14720 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Found 17 readings in extended date range
2025-05-27T15:20:58.382+02:00  INFO 14720 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Generated 16 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00]
2025-05-27T15:20:58.382+02:00  INFO 14720 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 16 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00]
2025-05-27T15:20:58.382+02:00  INFO 14720 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-27T15:00:00.224037 = 1546.947
2025-05-27T15:20:58.398+02:00  INFO 14720 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0, 15:00 - 16:00=0.0}
2025-05-27T15:20:58.398+02:00  INFO 14720 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T15:20:58.398783300
2025-05-27T15:20:58.462+02:00  INFO 14720 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-27T15:20:58.398783300 using time unit DAY
2025-05-27T15:20:58.462+02:00  INFO 14720 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T15:20:58.527+02:00  INFO 14720 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Found 664 readings in extended date range
2025-05-27T15:20:58.527+02:00  INFO 14720 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T15:20:58.527+02:00  INFO 14720 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T15:20:58.527+02:00  INFO 14720 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-27T15:00:00.224037 = 1546.947
2025-05-27T15:20:58.527+02:00  INFO 14720 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=0.27197266}
2025-05-27T15:20:58.545+02:00  INFO 14720 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T15:20:58.605+02:00  INFO 14720 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T15:20:58.621+02:00  INFO 14720 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T15:20:58.684+02:00  INFO 14720 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Found 4230 readings in extended date range
2025-05-27T15:20:58.684+02:00  INFO 14720 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T15:20:58.684+02:00  INFO 14720 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T15:20:58.684+02:00  INFO 14720 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-27T15:00:00.224037 = 1546.947
2025-05-27T15:20:58.700+02:00  INFO 14720 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T15:20:58.700+02:00  INFO 14720 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T15:20:58.700+02:00  INFO 14720 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=154.31006, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T15:30:29.577+02:00  INFO 14720 --- [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-27T15:30:29.582+02:00  INFO 14720 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-27T15:30:29.586+02:00  INFO 14720 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T15:30:29.589+02:00  INFO 14720 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-05-27T15:30:29.881+02:00  INFO 14720 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-05-27T19:28:03.528+02:00  INFO 20052 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 20052 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-27T19:28:03.530+02:00  INFO 20052 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-27T19:28:03.568+02:00  INFO 20052 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-05-27T19:28:03.569+02:00  INFO 20052 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-05-27T19:28:04.228+02:00  INFO 20052 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-27T19:28:04.308+02:00  INFO 20052 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 73 ms. Found 10 JPA repository interfaces.
2025-05-27T19:28:04.858+02:00  INFO 20052 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-27T19:28:04.869+02:00  INFO 20052 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-27T19:28:04.869+02:00  INFO 20052 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-27T19:28:04.907+02:00  INFO 20052 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-27T19:28:04.908+02:00  INFO 20052 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1339 ms
2025-05-27T19:28:05.103+02:00  INFO 20052 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-27T19:28:05.163+02:00  INFO 20052 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.15.Final
2025-05-27T19:28:05.197+02:00  INFO 20052 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-27T19:28:05.472+02:00  INFO 20052 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-27T19:28:05.500+02:00  INFO 20052 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-05-27T19:28:05.822+02:00  INFO 20052 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@6d7548d2
2025-05-27T19:28:05.824+02:00  INFO 20052 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-05-27T19:28:05.892+02:00  INFO 20052 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-27T19:28:06.788+02:00  INFO 20052 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-27T19:28:08.439+02:00  INFO 20052 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T19:28:08.471+02:00  WARN 20052 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-27T19:28:08.677+02:00  INFO 20052 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-05-27T19:28:10.291+02:00  WARN 20052 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-27T19:28:10.322+02:00  INFO 20052 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-27T19:28:10.532+02:00  INFO 20052 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-27T19:28:11.004+02:00  INFO 20052 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-27T19:28:11.042+02:00  INFO 20052 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-27T19:28:11.049+02:00  INFO 20052 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 7.918 seconds (process running for 8.795)
2025-05-27T19:29:57.552+02:00  INFO 20052 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-27T19:29:57.553+02:00  INFO 20052 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-27T19:29:57.554+02:00  INFO 20052 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-05-27T19:30:02.221+02:00  WARN 20052 --- [http-nio-8080-exec-6] c.h.evmodbus.services.ReportingService   : No serial ports found
2025-05-27T19:30:02.221+02:00  WARN 20052 --- [http-nio-8080-exec-6] c.h.evmodbus.services.ReportingService   : No serial ports found
2025-05-27T19:30:05.428+02:00  INFO 20052 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 72 from 2025-05-27T00:00 to 2025-05-27T19:30:05.428814500
2025-05-27T19:30:05.428+02:00  INFO 20052 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : No readings found for spot 72 in date range 2025-05-27T00:00 to 2025-05-27T19:30:05.428814500
2025-05-27T19:30:05.444+02:00  INFO 20052 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 72 from 2025-05-01T00:00 to 2025-05-27T19:30:05.444723900
2025-05-27T19:30:05.447+02:00  INFO 20052 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 72 from 2025-05-01T00:00 to 2025-05-27T19:30:05.444723900 using time unit DAY
2025-05-27T19:30:05.447+02:00  INFO 20052 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T19:30:05.449+02:00  INFO 20052 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Found 39 readings in extended date range
2025-05-27T19:30:05.449+02:00  INFO 20052 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T19:30:05.450+02:00  INFO 20052 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T19:30:05.450+02:00  INFO 20052 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-09T18:00:01.954083 = 10.936, Last reading: 2025-05-15T18:00:00.196100 = 10.936
2025-05-27T19:30:05.453+02:00  INFO 20052 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=0.0, 02/05=0.0, 03/05=0.0, 04/05=0.0, 05/05=0.0, 06/05=0.0, 07/05=0.0, 08/05=0.0, 09/05=0.0, 10/05=0.0, 11/05=0.0, 12/05=0.0, 13/05=0.0, 14/05=0.0, 15/05=0.0, 16/05=0.0, 17/05=0.0, 18/05=0.0, 19/05=0.0, 20/05=0.0, 21/05=0.0, 22/05=0.0, 23/05=0.0, 24/05=0.0, 25/05=0.0, 26/05=0.0, 27/05=0.0}
2025-05-27T19:30:05.460+02:00  INFO 20052 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 72 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T19:30:05.463+02:00  INFO 20052 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 72 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T19:30:05.463+02:00  INFO 20052 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T19:30:05.466+02:00  INFO 20052 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Found 39 readings in extended date range
2025-05-27T19:30:05.467+02:00  INFO 20052 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T19:30:05.467+02:00  INFO 20052 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T19:30:05.467+02:00  INFO 20052 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-09T18:00:01.954083 = 10.936, Last reading: 2025-05-15T18:00:00.196100 = 10.936
2025-05-27T19:30:05.468+02:00  INFO 20052 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=0.0, Feb=0.0, Mar=0.0, Abr=0.0, May=0.0, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T19:30:05.468+02:00  INFO 20052 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T19:30:05.468+02:00  INFO 20052 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=0.0, Feb=0.0, Mar=0.0, Abr=0.0, May=0.0, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T19:30:05.470+02:00  INFO 20052 --- [http-nio-8080-exec-9] c.h.e.s.c.ConsumptionCalculationService  : No readings found for spot 72 in date range 2025-05-27T00:00 to 2025-05-27T23:59:59.999999999
2025-05-27T19:30:13.867+02:00  INFO 20052 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 72 from 2025-05-27T00:00 to 2025-05-27T19:30:13.867729800
2025-05-27T19:30:13.867+02:00  INFO 20052 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : No readings found for spot 72 in date range 2025-05-27T00:00 to 2025-05-27T19:30:13.867729800
2025-05-27T19:30:13.879+02:00  INFO 20052 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 72 from 2025-04-01T00:00 to 2025-04-30T23:59:59.999999999
2025-05-27T19:30:13.879+02:00  INFO 20052 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : No readings found for spot 72 in date range 2025-04-01T00:00 to 2025-04-30T23:59:59.999999999
2025-05-27T19:30:13.879+02:00  INFO 20052 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 72 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T19:30:13.879+02:00  INFO 20052 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 72 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T19:30:13.879+02:00  INFO 20052 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T19:30:13.879+02:00  INFO 20052 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Found 39 readings in extended date range
2025-05-27T19:30:13.879+02:00  INFO 20052 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T19:30:13.879+02:00  INFO 20052 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T19:30:13.879+02:00  INFO 20052 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-09T18:00:01.954083 = 10.936, Last reading: 2025-05-15T18:00:00.196100 = 10.936
2025-05-27T19:30:13.879+02:00  INFO 20052 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=0.0, Feb=0.0, Mar=0.0, Abr=0.0, May=0.0, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T19:30:13.879+02:00  INFO 20052 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T19:30:13.879+02:00  INFO 20052 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=0.0, Feb=0.0, Mar=0.0, Abr=0.0, May=0.0, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T19:30:13.879+02:00  INFO 20052 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : No readings found for spot 72 in date range 2025-05-27T00:00 to 2025-05-27T23:59:59.999999999
2025-05-27T19:30:17.186+02:00  INFO 20052 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 72 from 2025-05-27T00:00 to 2025-05-27T19:30:17.186132600
2025-05-27T19:30:17.186+02:00  INFO 20052 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : No readings found for spot 72 in date range 2025-05-27T00:00 to 2025-05-27T19:30:17.186132600
2025-05-27T19:30:17.186+02:00  INFO 20052 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 72 from 2025-03-01T00:00 to 2025-03-31T23:59:59.999999999
2025-05-27T19:30:17.197+02:00  INFO 20052 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : No readings found for spot 72 in date range 2025-03-01T00:00 to 2025-03-31T23:59:59.999999999
2025-05-27T19:30:17.197+02:00  INFO 20052 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 72 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T19:30:17.197+02:00  INFO 20052 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 72 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T19:30:17.197+02:00  INFO 20052 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T19:30:17.197+02:00  INFO 20052 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Found 39 readings in extended date range
2025-05-27T19:30:17.197+02:00  INFO 20052 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T19:30:17.197+02:00  INFO 20052 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T19:30:17.197+02:00  INFO 20052 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-09T18:00:01.954083 = 10.936, Last reading: 2025-05-15T18:00:00.196100 = 10.936
2025-05-27T19:30:17.197+02:00  INFO 20052 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=0.0, Feb=0.0, Mar=0.0, Abr=0.0, May=0.0, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T19:30:17.197+02:00  INFO 20052 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T19:30:17.197+02:00  INFO 20052 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=0.0, Feb=0.0, Mar=0.0, Abr=0.0, May=0.0, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T19:30:17.197+02:00  INFO 20052 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : No readings found for spot 72 in date range 2025-05-27T00:00 to 2025-05-27T23:59:59.999999999
2025-05-27T19:31:03.003+02:00  INFO 20052 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 72 from 2025-05-27T00:00 to 2025-05-27T19:31:03.003516900
2025-05-27T19:31:03.004+02:00  INFO 20052 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : No readings found for spot 72 in date range 2025-05-27T00:00 to 2025-05-27T19:31:03.003516900
2025-05-27T19:31:03.005+02:00  INFO 20052 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 72 from 2025-03-01T00:00 to 2025-03-31T23:59:59.999999999
2025-05-27T19:31:03.006+02:00  INFO 20052 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : No readings found for spot 72 in date range 2025-03-01T00:00 to 2025-03-31T23:59:59.999999999
2025-05-27T19:31:03.007+02:00  INFO 20052 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 72 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T19:31:03.008+02:00  INFO 20052 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 72 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T19:31:03.009+02:00  INFO 20052 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T19:31:03.010+02:00  INFO 20052 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Found 39 readings in extended date range
2025-05-27T19:31:03.010+02:00  INFO 20052 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T19:31:03.011+02:00  INFO 20052 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T19:31:03.011+02:00  INFO 20052 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-09T18:00:01.954083 = 10.936, Last reading: 2025-05-15T18:00:00.196100 = 10.936
2025-05-27T19:31:03.011+02:00  INFO 20052 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=0.0, Feb=0.0, Mar=0.0, Abr=0.0, May=0.0, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T19:31:03.011+02:00  INFO 20052 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T19:31:03.011+02:00  INFO 20052 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=0.0, Feb=0.0, Mar=0.0, Abr=0.0, May=0.0, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T19:31:03.013+02:00  INFO 20052 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : No readings found for spot 72 in date range 2025-05-27T00:00 to 2025-05-27T23:59:59.999999999
2025-05-27T19:33:12.795+02:00  INFO 20052 --- [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-27T19:33:12.799+02:00  INFO 20052 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-27T19:33:12.804+02:00  INFO 20052 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T19:33:12.806+02:00  INFO 20052 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-05-27T19:33:12.814+02:00  INFO 20052 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-05-27T19:45:15.426+02:00  INFO 16824 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 16824 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-27T19:45:15.428+02:00  INFO 16824 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-27T19:45:15.468+02:00  INFO 16824 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-05-27T19:45:15.469+02:00  INFO 16824 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-05-27T19:45:16.113+02:00  INFO 16824 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-27T19:45:16.191+02:00  INFO 16824 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 72 ms. Found 10 JPA repository interfaces.
2025-05-27T19:45:16.710+02:00  INFO 16824 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-27T19:45:16.721+02:00  INFO 16824 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-27T19:45:16.721+02:00  INFO 16824 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-27T19:45:16.759+02:00  INFO 16824 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-27T19:45:16.759+02:00  INFO 16824 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1290 ms
2025-05-27T19:45:16.943+02:00  INFO 16824 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-27T19:45:16.996+02:00  INFO 16824 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.15.Final
2025-05-27T19:45:17.024+02:00  INFO 16824 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-27T19:45:17.260+02:00  INFO 16824 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-27T19:45:17.285+02:00  INFO 16824 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-05-27T19:45:17.548+02:00  INFO 16824 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@1f2b5039
2025-05-27T19:45:17.550+02:00  INFO 16824 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-05-27T19:45:17.606+02:00  INFO 16824 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-27T19:45:18.470+02:00  INFO 16824 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-27T19:45:20.100+02:00  INFO 16824 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T19:45:20.131+02:00  WARN 16824 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-27T19:45:20.340+02:00  INFO 16824 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-05-27T19:45:22.084+02:00  WARN 16824 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-27T19:45:22.119+02:00  INFO 16824 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-27T19:45:22.332+02:00  INFO 16824 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-27T19:45:22.760+02:00  INFO 16824 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-27T19:45:22.793+02:00  INFO 16824 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-27T19:45:22.800+02:00  INFO 16824 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 7.757 seconds (process running for 8.557)
2025-05-27T19:45:30.819+02:00  INFO 16824 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-27T19:45:30.819+02:00  INFO 16824 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-27T19:45:30.820+02:00  INFO 16824 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-05-27T19:45:30.950+02:00  WARN 16824 --- [http-nio-8080-exec-1] c.h.evmodbus.services.ReportingService   : No serial ports found
2025-05-27T19:45:30.953+02:00  WARN 16824 --- [http-nio-8080-exec-1] c.h.evmodbus.services.ReportingService   : No serial ports found
2025-05-27T19:45:34.132+02:00  INFO 16824 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 72 from 2025-05-27T00:00 to 2025-05-27T19:45:34.132548200
2025-05-27T19:45:34.134+02:00  INFO 16824 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : No readings found for spot 72 in date range 2025-05-27T00:00 to 2025-05-27T19:45:34.132548200
2025-05-27T19:45:34.138+02:00  INFO 16824 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 72 from 2025-05-01T00:00 to 2025-05-27T19:45:34.138847600
2025-05-27T19:45:34.141+02:00  INFO 16824 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 72 from 2025-05-01T00:00 to 2025-05-27T19:45:34.138847600 using time unit DAY
2025-05-27T19:45:34.141+02:00  INFO 16824 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T19:45:34.143+02:00  INFO 16824 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Found 39 readings in extended date range
2025-05-27T19:45:34.143+02:00  INFO 16824 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T19:45:34.143+02:00  INFO 16824 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T19:45:34.143+02:00  INFO 16824 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-09T18:00:01.954083 = 10.936, Last reading: 2025-05-15T18:00:00.196100 = 10.936
2025-05-27T19:45:34.143+02:00  INFO 16824 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=0.0, 02/05=0.0, 03/05=0.0, 04/05=0.0, 05/05=0.0, 06/05=0.0, 07/05=0.0, 08/05=0.0, 09/05=0.0, 10/05=0.0, 11/05=0.0, 12/05=0.0, 13/05=0.0, 14/05=0.0, 15/05=0.0, 16/05=0.0, 17/05=0.0, 18/05=0.0, 19/05=0.0, 20/05=0.0, 21/05=0.0, 22/05=0.0, 23/05=0.0, 24/05=0.0, 25/05=0.0, 26/05=0.0, 27/05=0.0}
2025-05-27T19:45:34.143+02:00  INFO 16824 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 72 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T19:45:34.151+02:00  INFO 16824 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 72 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T19:45:34.151+02:00  INFO 16824 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T19:45:34.153+02:00  INFO 16824 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Found 39 readings in extended date range
2025-05-27T19:45:34.154+02:00  INFO 16824 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T19:45:34.154+02:00  INFO 16824 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T19:45:34.154+02:00  INFO 16824 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-09T18:00:01.954083 = 10.936, Last reading: 2025-05-15T18:00:00.196100 = 10.936
2025-05-27T19:45:34.155+02:00  INFO 16824 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=0.0, Feb=0.0, Mar=0.0, Abr=0.0, May=0.0, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T19:45:34.155+02:00  INFO 16824 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T19:45:34.155+02:00  INFO 16824 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=0.0, Feb=0.0, Mar=0.0, Abr=0.0, May=0.0, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T19:45:34.158+02:00  INFO 16824 --- [http-nio-8080-exec-4] c.h.e.s.c.ConsumptionCalculationService  : No readings found for spot 72 in date range 2025-05-27T00:00 to 2025-05-27T23:59:59.999999999
2025-05-27T19:45:42.973+02:00  INFO 16824 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 72 from 2025-05-27T00:00 to 2025-05-27T19:45:42.973012700
2025-05-27T19:45:42.973+02:00  INFO 16824 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : No readings found for spot 72 in date range 2025-05-27T00:00 to 2025-05-27T19:45:42.973012700
2025-05-27T19:45:42.973+02:00  INFO 16824 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 72 from 2025-05-01T00:00 to 2025-05-27T19:45:42.973012700
2025-05-27T19:45:42.973+02:00  INFO 16824 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 72 from 2025-05-01T00:00 to 2025-05-27T19:45:42.973012700 using time unit DAY
2025-05-27T19:45:42.973+02:00  INFO 16824 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T19:45:42.973+02:00  INFO 16824 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Found 39 readings in extended date range
2025-05-27T19:45:42.973+02:00  INFO 16824 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T19:45:42.973+02:00  INFO 16824 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T19:45:42.973+02:00  INFO 16824 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-09T18:00:01.954083 = 10.936, Last reading: 2025-05-15T18:00:00.196100 = 10.936
2025-05-27T19:45:42.973+02:00  INFO 16824 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=0.0, 02/05=0.0, 03/05=0.0, 04/05=0.0, 05/05=0.0, 06/05=0.0, 07/05=0.0, 08/05=0.0, 09/05=0.0, 10/05=0.0, 11/05=0.0, 12/05=0.0, 13/05=0.0, 14/05=0.0, 15/05=0.0, 16/05=0.0, 17/05=0.0, 18/05=0.0, 19/05=0.0, 20/05=0.0, 21/05=0.0, 22/05=0.0, 23/05=0.0, 24/05=0.0, 25/05=0.0, 26/05=0.0, 27/05=0.0}
2025-05-27T19:45:42.973+02:00  INFO 16824 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 72 from 2024-01-01T00:00 to 2024-12-31T23:59:59.999999999
2025-05-27T19:45:42.973+02:00  INFO 16824 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : No readings found for spot 72 in date range 2024-01-01T00:00 to 2024-12-31T23:59:59.999999999
2025-05-27T19:45:42.973+02:00  INFO 16824 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T19:45:42.973+02:00  INFO 16824 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=0.0, Feb=0.0, Mar=0.0, Abr=0.0, May=0.0, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T19:45:42.973+02:00  INFO 16824 --- [http-nio-8080-exec-10] c.h.e.s.c.ConsumptionCalculationService  : No readings found for spot 72 in date range 2025-05-27T00:00 to 2025-05-27T23:59:59.999999999
2025-05-27T19:45:47.499+02:00  INFO 16824 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 72 from 2025-05-27T00:00 to 2025-05-27T19:45:47.499775200
2025-05-27T19:45:47.499+02:00  INFO 16824 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : No readings found for spot 72 in date range 2025-05-27T00:00 to 2025-05-27T19:45:47.499775200
2025-05-27T19:45:47.499+02:00  INFO 16824 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 72 from 2025-05-01T00:00 to 2025-05-27T19:45:47.499775200
2025-05-27T19:45:47.499+02:00  INFO 16824 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 72 from 2025-05-01T00:00 to 2025-05-27T19:45:47.499775200 using time unit DAY
2025-05-27T19:45:47.499+02:00  INFO 16824 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T19:45:47.499+02:00  INFO 16824 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Found 39 readings in extended date range
2025-05-27T19:45:47.499+02:00  INFO 16824 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T19:45:47.499+02:00  INFO 16824 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T19:45:47.499+02:00  INFO 16824 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-09T18:00:01.954083 = 10.936, Last reading: 2025-05-15T18:00:00.196100 = 10.936
2025-05-27T19:45:47.499+02:00  INFO 16824 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=0.0, 02/05=0.0, 03/05=0.0, 04/05=0.0, 05/05=0.0, 06/05=0.0, 07/05=0.0, 08/05=0.0, 09/05=0.0, 10/05=0.0, 11/05=0.0, 12/05=0.0, 13/05=0.0, 14/05=0.0, 15/05=0.0, 16/05=0.0, 17/05=0.0, 18/05=0.0, 19/05=0.0, 20/05=0.0, 21/05=0.0, 22/05=0.0, 23/05=0.0, 24/05=0.0, 25/05=0.0, 26/05=0.0, 27/05=0.0}
2025-05-27T19:45:47.499+02:00  INFO 16824 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 72 from 2023-01-01T00:00 to 2023-12-31T23:59:59.999999999
2025-05-27T19:45:47.499+02:00  INFO 16824 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : No readings found for spot 72 in date range 2023-01-01T00:00 to 2023-12-31T23:59:59.999999999
2025-05-27T19:45:47.499+02:00  INFO 16824 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T19:45:47.499+02:00  INFO 16824 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=0.0, Feb=0.0, Mar=0.0, Abr=0.0, May=0.0, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T19:45:47.499+02:00  INFO 16824 --- [http-nio-8080-exec-1] c.h.e.s.c.ConsumptionCalculationService  : No readings found for spot 72 in date range 2025-05-27T00:00 to 2025-05-27T23:59:59.999999999
2025-05-27T19:50:25.586+02:00  INFO 16824 --- [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 48 class path changes (0 additions, 0 deletions, 48 modifications)
2025-05-27T19:50:25.593+02:00  INFO 16824 --- [Thread-6] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-27T19:50:25.593+02:00  INFO 16824 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-27T19:50:25.593+02:00  INFO 16824 --- [Thread-6] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T19:50:25.602+02:00  INFO 16824 --- [Thread-6] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-05-27T19:50:25.602+02:00  INFO 16824 --- [Thread-6] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-05-27T19:50:25.681+02:00  INFO 16824 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 16824 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-27T19:50:25.681+02:00  INFO 16824 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-27T19:50:25.967+02:00  INFO 16824 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-27T19:50:25.995+02:00  INFO 16824 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 35 ms. Found 10 JPA repository interfaces.
2025-05-27T19:50:26.132+02:00  INFO 16824 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-27T19:50:26.132+02:00  INFO 16824 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-27T19:50:26.132+02:00  INFO 16824 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-27T19:50:26.153+02:00  INFO 16824 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-27T19:50:26.154+02:00  INFO 16824 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 473 ms
2025-05-27T19:50:26.242+02:00  INFO 16824 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-27T19:50:26.245+02:00  INFO 16824 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-27T19:50:26.252+02:00  INFO 16824 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-27T19:50:26.252+02:00  INFO 16824 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-05-27T19:50:26.335+02:00  INFO 16824 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@6e915a5a
2025-05-27T19:50:26.335+02:00  INFO 16824 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-05-27T19:50:26.342+02:00  INFO 16824 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-2)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-27T19:50:26.558+02:00  INFO 16824 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-27T19:50:28.149+02:00  INFO 16824 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T19:50:28.179+02:00  WARN 16824 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-27T19:50:28.635+02:00  WARN 16824 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-27T19:50:28.655+02:00  INFO 16824 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-27T19:50:28.774+02:00  INFO 16824 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-27T19:50:28.991+02:00  INFO 16824 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-27T19:50:29.006+02:00  INFO 16824 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-27T19:50:29.010+02:00  INFO 16824 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 3.361 seconds (process running for 314.767)
2025-05-27T19:50:29.053+02:00  INFO 16824 --- [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-05-27T19:56:05.686+02:00  INFO 16824 --- [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-27T19:56:05.690+02:00  INFO 16824 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-27T19:56:05.693+02:00  INFO 16824 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T19:56:05.693+02:00  INFO 16824 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-05-27T19:56:05.698+02:00  INFO 16824 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-05-27T19:56:09.066+02:00  INFO 6560 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 6560 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-27T19:56:09.067+02:00  INFO 6560 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-27T19:56:09.108+02:00  INFO 6560 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-05-27T19:56:09.108+02:00  INFO 6560 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-05-27T19:56:09.738+02:00  INFO 6560 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-27T19:56:09.813+02:00  INFO 6560 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 68 ms. Found 10 JPA repository interfaces.
2025-05-27T19:56:10.334+02:00  INFO 6560 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-27T19:56:10.345+02:00  INFO 6560 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-27T19:56:10.345+02:00  INFO 6560 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-27T19:56:10.383+02:00  INFO 6560 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-27T19:56:10.383+02:00  INFO 6560 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1275 ms
2025-05-27T19:56:10.562+02:00  INFO 6560 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-27T19:56:10.608+02:00  INFO 6560 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.15.Final
2025-05-27T19:56:10.636+02:00  INFO 6560 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-27T19:56:10.876+02:00  INFO 6560 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-27T19:56:10.902+02:00  INFO 6560 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-05-27T19:56:11.169+02:00  INFO 6560 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@7776cc14
2025-05-27T19:56:11.170+02:00  INFO 6560 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-05-27T19:56:11.233+02:00  INFO 6560 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-27T19:56:12.123+02:00  INFO 6560 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-27T19:56:13.767+02:00  INFO 6560 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T19:56:13.798+02:00  WARN 6560 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-27T19:56:14.014+02:00  INFO 6560 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-05-27T19:56:15.838+02:00  WARN 6560 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-27T19:56:15.871+02:00  INFO 6560 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-27T19:56:16.106+02:00  INFO 6560 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-27T19:56:16.719+02:00  INFO 6560 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-27T19:56:16.760+02:00  INFO 6560 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-27T19:56:16.767+02:00  INFO 6560 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 8.072 seconds (process running for 9.44)
2025-05-27T19:56:25.703+02:00  INFO 6560 --- [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 46 class path changes (0 additions, 0 deletions, 46 modifications)
2025-05-27T19:56:25.707+02:00  INFO 6560 --- [Thread-6] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-27T19:56:25.711+02:00  INFO 6560 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-27T19:56:25.717+02:00  INFO 6560 --- [Thread-6] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T19:56:25.719+02:00  INFO 6560 --- [Thread-6] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-05-27T19:56:25.728+02:00  INFO 6560 --- [Thread-6] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-05-27T19:56:25.875+02:00  INFO 6560 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 6560 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-27T19:56:25.876+02:00  INFO 6560 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-27T19:56:26.157+02:00  INFO 6560 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-27T19:56:26.194+02:00  INFO 6560 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 36 ms. Found 10 JPA repository interfaces.
2025-05-27T19:56:26.317+02:00  INFO 6560 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-27T19:56:26.318+02:00  INFO 6560 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-27T19:56:26.318+02:00  INFO 6560 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-27T19:56:26.342+02:00  INFO 6560 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-27T19:56:26.342+02:00  INFO 6560 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 458 ms
2025-05-27T19:56:26.447+02:00  INFO 6560 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-27T19:56:26.453+02:00  INFO 6560 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-27T19:56:26.468+02:00  INFO 6560 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-27T19:56:26.469+02:00  INFO 6560 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-05-27T19:56:26.551+02:00  INFO 6560 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@4b756357
2025-05-27T19:56:26.551+02:00  INFO 6560 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-05-27T19:56:26.562+02:00  INFO 6560 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-2)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-27T19:56:26.779+02:00  INFO 6560 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-27T19:56:28.387+02:00  INFO 6560 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T19:56:28.414+02:00  WARN 6560 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-27T19:56:28.838+02:00  WARN 6560 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-27T19:56:28.866+02:00  INFO 6560 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-27T19:56:29.034+02:00  INFO 6560 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-27T19:56:29.433+02:00  INFO 6560 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-27T19:56:29.450+02:00  INFO 6560 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-27T19:56:29.450+02:00  INFO 6560 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 3.644 seconds (process running for 22.126)
2025-05-27T19:56:29.505+02:00  INFO 6560 --- [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-05-27T19:57:20.189+02:00  INFO 6560 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-27T19:57:20.189+02:00  INFO 6560 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-27T19:57:20.189+02:00  INFO 6560 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 0 ms
2025-05-27T19:57:27.959+02:00  INFO 6560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 72 from 2025-05-27T00:00 to 2025-05-27T19:57:27.959628700
2025-05-27T19:57:27.961+02:00  INFO 6560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : No readings found for spot 72 in date range 2025-05-27T00:00 to 2025-05-27T19:57:27.959628700
2025-05-27T19:57:27.965+02:00  INFO 6560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 72 from 2025-05-01T00:00 to 2025-05-27T19:57:27.965635300
2025-05-27T19:57:27.967+02:00  INFO 6560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 72 from 2025-05-01T00:00 to 2025-05-27T19:57:27.965635300 using time unit DAY
2025-05-27T19:57:27.967+02:00  INFO 6560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-28T23:59:59
2025-05-27T19:57:27.970+02:00  INFO 6560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Found 39 readings in extended date range
2025-05-27T19:57:27.970+02:00  INFO 6560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Generated 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T19:57:27.970+02:00  INFO 6560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 27 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05]
2025-05-27T19:57:27.970+02:00  INFO 6560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-09T18:00:01.954083 = 10.936, Last reading: 2025-05-15T18:00:00.196100 = 10.936
2025-05-27T19:57:27.972+02:00  INFO 6560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=0.0, 02/05=0.0, 03/05=0.0, 04/05=0.0, 05/05=0.0, 06/05=0.0, 07/05=0.0, 08/05=0.0, 09/05=0.0, 10/05=0.0, 11/05=0.0, 12/05=0.0, 13/05=0.0, 14/05=0.0, 15/05=0.0, 16/05=0.0, 17/05=0.0, 18/05=0.0, 19/05=0.0, 20/05=0.0, 21/05=0.0, 22/05=0.0, 23/05=0.0, 24/05=0.0, 25/05=0.0, 26/05=0.0, 27/05=0.0}
2025-05-27T19:57:27.981+02:00  INFO 6560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 72 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-27T19:57:27.985+02:00  INFO 6560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 72 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-27T19:57:27.987+02:00  INFO 6560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-27T19:57:27.989+02:00  INFO 6560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Found 39 readings in extended date range
2025-05-27T19:57:27.990+02:00  INFO 6560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T19:57:27.990+02:00  INFO 6560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-27T19:57:27.990+02:00  INFO 6560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-09T18:00:01.954083 = 10.936, Last reading: 2025-05-15T18:00:00.196100 = 10.936
2025-05-27T19:57:27.991+02:00  INFO 6560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=0.0, Feb=0.0, Mar=0.0, Abr=0.0, May=0.0, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T19:57:27.991+02:00  INFO 6560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-27T19:57:27.991+02:00  INFO 6560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=0.0, Feb=0.0, Mar=0.0, Abr=0.0, May=0.0, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-27T19:57:27.995+02:00  INFO 6560 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : No readings found for spot 72 in date range 2025-05-27T00:00 to 2025-05-27T23:59:59.999999999
2025-05-27T19:57:38.639+02:00  INFO 6560 --- [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-27T19:57:38.642+02:00  INFO 6560 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-27T19:57:38.644+02:00  INFO 6560 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-27T19:57:38.644+02:00  INFO 6560 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-05-27T19:57:38.650+02:00  INFO 6560 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
